<template>
  <div class="work-report-container">
    <!-- 顶部标签页导航 -->
    <div class="tab-nav">
      <back-button />
      <div class="tab-item active">汇报</div>
      <div class="tab-item" @click="goToApproval">审批</div>
      <div class="tab-item" @click="goToStatistics">统计</div>
      
      <!-- 添加开发调试按钮，仅在开发环境显示 -->
      <div v-if="isDevelopment" class="dev-tools">
        <!-- 强制刷新项目按钮已移除 -->
        <!-- 测试项目数据API按钮已移除 -->
      </div>
    </div>

    <!-- 日历区域 -->
    <div class="calendar-container">
      <div class="calendar-header">
        <van-icon name="arrow-left" @click="goToPreviousPeriod" />
        <div class="calendar-title">{{ calendarTitle }}</div>
        <van-icon name="arrow" @click="goToNextPeriod" />
      </div>
      
      <div class="calendar-nav">
        <div 
          class="week-day" 
          v-for="day in weekDays" 
          :key="day"
        >{{ day }}</div>
      </div>
      
      <!-- 周视图日历 -->
      <div class="calendar-days" v-if="calendarMode === 'week'">
        <div 
          v-for="day in calendarDays" 
          :key="day.date"
          class="calendar-day week-mode-day" 
          :class="{ 'current-day': day.isCurrentDay, 'selected-day': isSelectedDay(day.date) }"
          @click="selectDate(day.date)"
        >
          <div class="day-number">{{ day.dayNumber }}</div>
          <!-- 根据任务状态显示不同的指示器 -->
          <van-icon v-if="day.taskStatus === 'approved'" name="success" class="task-approved-icon" />
          <div v-else-if="day.taskStatus === 'none'" class="task-pending-indicator"></div>
        </div>
      </div>
      
      <!-- 月视图日历 -->
      <div class="calendar-days month-calendar" v-else-if="calendarMode === 'month'"
           @touchstart="handleTouchStart"
           @touchmove="handleTouchMove" 
           @touchend="handleTouchEnd">
        <div 
          v-for="day in calendarDays" 
          :key="day.date || 'empty-' + Math.random()"
          class="calendar-day month-mode-day" 
          :class="{ 
            'current-day': day.isCurrentDay, 
            'selected-day': !day.isEmpty && isSelectedDay(day.date),
            'empty-day': day.isEmpty,
            'other-month-day': day.isOtherMonth
          }"
          @click="!day.isEmpty && selectDate(day.date)"
        >
          <div class="day-number" v-if="!day.isEmpty">{{ day.dayNumber }}</div>
          <!-- 根据任务状态显示不同的指示器 -->
          <van-icon v-if="!day.isEmpty && day.taskStatus === 'approved'" name="success" class="task-approved-icon" />
          <div v-else-if="!day.isEmpty && day.taskStatus === 'none'" class="task-pending-indicator"></div>
        </div>
      </div>
      
      <!-- 年视图日历 -->
      <div class="year-calendar" v-else>
        <div class="year-months">
          <div 
            v-for="month in yearMonths" 
            :key="month.index"
            class="year-month" 
            :class="{ 'current-month': month.isCurrent }"
            @click="selectMonth(month.index)"
          >
            {{ month.name }}
          </div>
        </div>
      </div>
      
      <div class="calendar-mode-selector">
        <van-icon 
          :name="calendarMode === 'week' ? 'arrow-down' : 'arrow-up'" 
          @click="toggleWeekMonthMode"
          class="mode-toggle-arrow"
        />
      </div>
    </div>

    <!-- 任务列表区域 -->
    <div class="task-list-container">
      <div class="task-date-header">
        <div class="task-stats">
          暂存/已提交/已审核 {{ taskStatusStats }}
        </div>
        <div class="action-buttons">
          <van-button type="primary" size="small" class="action-btn add-task-btn" @click="showAddTaskPopup">添加任务</van-button>
          <van-button type="primary" size="small" class="action-btn today-task-btn" @click="showTodayTasksPopup">今日任务</van-button>
        </div>
      </div>
      
      <!-- 卡片模式 -->
      <div class="task-list" v-if="tasks.length > 0">
        <!-- 项目分组显示 -->
        <div class="project-group" v-for="group in filteredGroupedTasks" :key="group.projectName">
          <!-- 项目组头部 -->
          <div class="project-group-header" @click="toggleProjectGroup(group.projectName)">
            <div class="project-info">
              <div class="project-name">{{ group.projectName }}</div>
            </div>
            <div class="project-toggle" :style="{ 'margin-right': '3px' }">
              <van-icon :name="group.isExpanded ? 'arrow-up' : 'arrow-down'" />
            </div>
          </div>
          
          <!-- 项目组内容 -->
          <div class="project-group-content" v-show="group.isExpanded">
            <div class="task-item" v-for="task in group.tasks" :key="task.id" @click="viewTaskDetails(task)">
              <div class="task-content">
                <div class="task-name">
                  <span class="task-name-content">
                    <span v-if="task.isProjectLevel" class="project-level-tag">项目</span>
                    <span v-else-if="task.taskType === 'agile'">
                      <span class="agile-task-name">{{ task.taskName }}</span>
                      <span class="agile-task-tag">敏捷任务</span>
                    </span>
                    <!-- 临时任务和耗费任务的名称支持内联编辑 -->
                    <span v-else-if="task.taskType === 'temp' || task.taskType === 'timecost'" class="editable-task-name" @click.stop>
                      <span
                        v-if="editingTaskId === task.id && editingField === 'taskName' && canEditTask(task)"
                        class="inline-editing"
                      >
                        <input
                          v-model="task.taskName"
                          type="text"
                          maxlength="50"
                          @blur="stopEditing"
                          @keyup.enter="stopEditing"
                          ref="editingInput"
                          class="custom-input task-name-input"
                          placeholder="请输入任务名称"
                        />
                      </span>
                      <span
                        v-else
                        :class="['editable-value', { 'disabled-field': !canEditTask(task) }]"
                        @click="canEditTask(task) && startEditing(task.id, 'taskName')"
                        :title="canEditTask(task) ? '点击编辑任务名称' : '已提交或已审核的任务不可编辑'"
                      >
                        {{ task.taskName || '点击编辑任务名称' }} <span v-if="canEditTask(task)" class="edit-hint"></span>
                      </span>
                    </span>
                    <!-- 普通任务名称保持只读 -->
                    <span v-else>{{ task.taskName }}</span>
                  </span>
                  <span class="task-status-tag" :class="[getStatusClass(task.status)]">{{ formatTaskStatus(task.status) }}</span>
                  <!-- 操作按钮移动到任务名称行内 -->
                  <div class="task-actions" @click.stop v-if="canDeleteTask(task) || canUndoTask(task)">
                    <van-icon name="delete" @click="deleteTask(task)" v-if="canDeleteTask(task)" />
                    <van-icon name="revoke" @click="undoTask(task)" v-if="canUndoTask(task)" />
                  </div>
                </div>
                
                <div class="task-info" @click.stop>
                  <!-- 计划工时 - 只读显示 -->
                  <span class="task-planned-hours readonly-field">
                    <span class="field-label">计划: </span>
                    <span class="readonly-value">
                      {{ task.plannedHours || 0 }}小时
                    </span>
                  </span>
                  
                  <!-- 已报工时 - 只读显示 -->
                  <span class="task-reported-hours readonly-field">
                    <span class="field-label">已报: </span>
                    <span class="readonly-value">
                      {{ getTaskDisplayReportedHours(task) }}小时
                    </span>
                  </span>
                  <!-- 类型字段：仅临时/耗费任务显示，紧跟已报后面 -->
                  <span v-if="task.taskType === 'temp'" class="task-tmp-type editable-field">
                    <span class="field-label">类型: </span>
                    <span 
                      class="editable-value"
                      @click="startTmpTypeEditing(task.id)"
                      title="点击选择临时任务类型"
                    >
                      {{ task.tmpTaskTypeName || '临时任务' }} <span class="edit-hint"></span>
                    </span>
                  </span>
                  <span v-else-if="task.taskType === 'timecost'" class="task-burnoff-type editable-field">
                    <span class="field-label">类型: </span>
                    <span 
                      class="editable-value"
                      @click="startBurnOffTypeEditing(task.id)"
                      title="点击选择耗费类型"
                    >
                      {{ task.burnOffTypeName || '耗费' }} <span class="edit-hint"></span>
                    </span>
                  </span>
                  <!-- 岗位字段：仅普通任务显示 -->
                  <span v-if="task.taskType !== 'temp' && task.taskType !== 'timecost'" class="task-position editable-field" :style="{ opacity: positionOptions.length > 1 ? 1 : 0.6, cursor: positionOptions.length > 1 ? 'pointer' : 'default' }">
                    <span class="field-label">岗位: </span>
                    <span 
                      class="editable-value"
                      @click="showInlinePositionPicker = true"
                      title="点击选择岗位"
                    >
                      {{ task.positionName || task.position || (positionOptions.length > 1 ? '点击选择岗位' : task.position) }}
                      <span class="edit-hint"></span>
                    </span>
                  </span>
                  
                  <!-- 汇报工时 - 可内联编辑 -->
                  <span class="task-hours editable-field">
                    <span class="field-label">汇报: </span>
                    <span
                      v-if="editingTaskId === task.id && editingField === 'regularHours' && canEditTask(task)"
                      class="inline-editing"
                    >
                      <input
                        v-model.number="task.regularHours"
                        type="number"
                        step="0.5"
                        min="0"
                        max="24"
                        @blur="stopEditing"
                        @keyup.enter="stopEditing"
                        ref="editingInput"
                        class="custom-input hours-input"
                        placeholder="点击输入"
                      />小时
                    </span>
                    <span
                      v-else
                      :class="['editable-value', { 'disabled-field': !canEditTask(task) }]"
                      @click="canEditTask(task) && startEditing(task.id, 'regularHours')"
                      :title="canEditTask(task) ? '点击编辑汇报工时' : '已提交或已审核的任务不可编辑'"
                    >
                      {{ task.regularHours ? task.regularHours + '小时' : '点击输入' }} <span v-if="canEditTask(task)" class="edit-hint"></span>
                    </span>
                  </span>
                  
                  <!-- 加班工时 - 可内联编辑 -->
                  <span class="task-overtime editable-field">
                    <span class="field-label">加班: </span>
                    <span
                      v-if="editingTaskId === task.id && editingField === 'overtimeHours' && canEditTask(task)"
                      class="inline-editing"
                    >
                      <input
                        v-model.number="task.overtimeHours"
                        type="number"
                        step="0.5"
                        min="0"
                        max="24"
                        @blur="stopEditing"
                        @keyup.enter="stopEditing"
                        ref="editingInput"
                        class="custom-input hours-input"
                        placeholder="点击输入"
                      />小时
                    </span>
                    <span
                      v-else
                      :class="['editable-value', { 'disabled-field': !canEditTask(task) }]"
                      @click="canEditTask(task) && startEditing(task.id, 'overtimeHours')"
                      :title="canEditTask(task) ? '点击编辑加班工时' : '已提交或已审核的任务不可编辑'"
                    >
                      {{ (task.overtimeHours || 0) + '小时' }} <span v-if="canEditTask(task)" class="edit-hint"></span>
                    </span>
                  </span>
                  
                  <!-- 进度 - 可内联编辑 -->
                  <span class="task-progress editable-field">
                    <span class="field-label">进度: </span>
                    <span
                      :class="['editable-value', { 'disabled-field': !canEditTask(task) }]"
                      @click="canEditTask(task) && (startEditing(task.id, 'progress'), showInlineProgressPicker = true)"
                      :title="canEditTask(task) ? '点击编辑进度' : '已提交或已审核的任务不可编辑'"
                    >
                      {{ task.progress.toFixed(0) }}% <span v-if="canEditTask(task)" class="edit-hint"></span>
                    </span>
                  </span>
                  
                  <span class="task-ownproject" v-if="task.ownProject">FOWNPROJECT: {{ task.ownProject }}</span>
                </div>

                <!-- 工作内容 - 使用统一的编辑组件 -->
                <div class="task-description">
                  <WorkContentEditor
                    :content="task.taskContent"
                    @update:content="updateTaskContent(task.id, $event)"
                    @edit-start="handleContentEditStart(task.id)"
                    @edit-end="handleContentEditEnd(task.id)"
                    :placeholder="canEditTask(task) ? '点击编辑工作内容' : '已提交或已审核的任务不可编辑'"
                    :disabled="!canEditTask(task)"
                    :min-height="20"
                    :max-height="200"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="no-data" v-if="tasks.length === 0">
        <van-empty image="search" description="没有与条件匹配的数据" />
      </div>
    </div>

    <!-- 添加任务弹窗 -->
    <van-popup v-model="showTaskForm" position="bottom" :style="{ height: '60%' }">
      <div class="task-form">
        <div class="form-header">
          <div class="form-title">{{ isEditingTask ? '编辑任务' : '添加任务' }}</div>
          <van-icon name="cross" @click="closeTaskForm" />
        </div>
        
        <div class="form-content">
          <!-- 项目/任务选择 -->
          <div class="form-item">
            <div class="form-label">项目/任务</div>
            <div class="field-with-action">
              <van-field
                v-if="taskForm.taskName"
                v-model="taskForm.taskName"
                readonly
                placeholder="请选择项目或任务"
                right-icon="arrow"
                @click="showCombinedPicker = true"
              >
                <template #label>
                  <span class="field-label">任务</span>
                </template>
              </van-field>
              <van-field
                v-else
                v-model="taskForm.projectName"
                readonly
                placeholder="请选择项目或任务"
                right-icon="arrow"
                @click="showCombinedPicker = true"
              >
                <template #label>
                  <span class="field-label">项目</span>
                </template>
              </van-field>
            </div>
            
            <!-- 项目级别工作选项 -->
            <div v-if="taskForm.project && !taskForm.taskName" class="project-level-option">
              <van-checkbox v-model="taskForm.isProjectLevel" shape="square">项目（不选择具体任务）</van-checkbox>
            </div>
            
            <!-- 任务选择提示 -->
            <div v-if="taskForm.project && !taskForm.taskName && !taskForm.isProjectLevel" class="task-select-tip">
              <van-icon name="info-o" /> 请选择具体任务或勾选"项目工作"
            </div>
          </div>
          
          <!-- 工作内容 -->
          <div class="form-item">
            <div class="form-label">工作内容</div>
            <textarea
              v-model="taskForm.taskContent"
              @input="autoResize"
              placeholder="请输入工作内容"
              class="custom-textarea form-textarea"
            ></textarea>
          </div>
          
          <!-- 汇报工时 -->
          <div class="form-item">
            <div class="form-label">汇报工时（小时）</div>
            <div class="hours-input">
              <van-stepper v-model="taskForm.regularHours" step="0.5" min="0.5" max="24" />
            </div>
          </div>
          
          <!-- 加班工时 -->
          <div class="form-item">
            <div class="form-label">加班工时（小时）</div>
            <div class="hours-input">
              <van-stepper v-model="taskForm.overtimeHours" step="0.5" min="0" max="24" />
            </div>
          </div>
          
          <!-- 进度 -->
          <div class="form-item">
            <div class="form-label">进度</div>
            <van-field
              :value="taskForm.progress + '%'"
              is-link
              readonly
              placeholder="请选择进度"
              @click="showProgressPicker = true"
            />
          </div>
          
          <!-- 岗位 -->
          <div class="form-item" v-if="positionOptions.length > 1">
            <div class="form-label">岗位</div>
            <van-field
              v-model="taskForm.positionName"
              is-link
              readonly
              placeholder="请选择岗位"
              @click="showPositionPicker = true"
            />
            <van-popup v-model="showPositionPicker" position="bottom">
              <van-picker
                show-toolbar
                title="选择岗位"
                :columns="positionOptions"
                value-key="text"
                @confirm="onPositionConfirm"
                @cancel="showPositionPicker = false"
              />
            </van-popup>
          </div>
          
          <!-- 计划工时 -->
          <div class="form-item">
            <div class="form-label">计划工时（小时）</div>
            <div class="hours-input">
              <van-stepper v-model="taskForm.plannedHours" step="0.5" min="0" max="1000" disabled />
            </div>
          </div>
        </div>
        
        <div class="form-footer">
          <van-button block type="primary" @click="saveTask">保存</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <van-button plain type="primary" class="save-btn" @click="saveAllTasks">保存</van-button>
      <van-button type="primary" class="submit-btn" @click="submitTasks">提交</van-button>
    </div>

    <!-- 任务详情弹窗 -->
    <van-popup v-model="showTaskDetails" position="bottom" :style="{ height: '50%' }">
      <div class="task-details">
        <div class="details-header">
          <div class="details-title">任务详情</div>
          <div class="details-actions">
            <van-icon v-if="canEditTask(taskDetails)" name="edit" @click="editTaskFromDetails" />
            <van-icon name="cross" @click="closeTaskDetails" />
          </div>
        </div>
        
        <div class="details-content" v-if="taskDetails">
          <div class="details-item">
            <div class="details-label">项目</div>
            <div class="details-value">{{ taskDetails.projectName || taskDetails.project }}</div>
          </div>
          
          <div class="details-item">
            <div class="details-label">任务类型</div>
            <div class="details-value">
              <span v-if="taskDetails.isProjectLevel" class="project-level-tag">项目工作</span>
              <span v-else-if="taskDetails.taskType === 'agile'" class="agile-task-tag">敏捷任务</span>
              <span v-else>常规任务</span>
            </div>
          </div>
          
          <!-- 添加FOWNPROJECT相关信息 -->
          <div class="details-item" v-if="taskDetails.ownProject">
            <div class="details-label">所属项目关联</div>
            <div class="details-value">
              <div class="fownproject-info">
                <div>
                  <span class="info-label">FOWNPROJECT值: </span>
                  <span class="info-value">{{ taskDetails.ownProject }}</span>
                </div>
                <div v-if="taskDetails.matchType">
                  <span class="info-label">关联方式: </span>
                  <span class="info-value match-type">{{ taskDetails.matchType || '未知' }}</span>
                </div>
                <div v-if="taskDetails.projectName && taskDetails.ownProject !== taskDetails.projectName" class="mapping-warning">
                  <van-icon name="warning-o" color="#ff976a" />
                  <span>所属项目与当前项目不一致</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="details-item" v-if="!taskDetails.isProjectLevel">
            <div class="details-label">任务名称</div>
            <div class="details-value">{{ taskDetails.taskName }}</div>
          </div>
          
          <div class="details-item">
            <div class="details-label">工作内容</div>
            <div class="details-value">{{ taskDetails.taskContent }}</div>
          </div>
          
          <div class="details-item">
            <div class="details-label">计划工时</div>
            <div class="details-value">{{ taskDetails.plannedHours || '未设置' }} 小时</div>
          </div>
          
          <div class="details-item">
            <div class="details-label">已报工时</div>
            <div class="details-value">
              <span v-if="taskDetailsReportedHoursLoading" class="loading-text">
                <van-loading size="14px" /> 加载中...
              </span>
              <span v-else-if="taskDetailsReportedHoursError" class="error-text">
                获取失败: {{ taskDetailsReportedHoursError }}
              </span>
              <span v-else>{{ taskDetailsReportedHours }} 小时</span>
              <span class="details-hours-breakdown">(专用API获取的真实已报工时)</span>
            </div>
          </div>
          
          <div class="details-item">
            <div class="details-label">进度</div>
            <div class="details-value">
              <div class="progress-bar-container">
                <div class="progress-bar" :style="{ width: `${taskDetails.progress}%` }"></div>
              </div>
              <span class="progress-text">{{ taskDetails.progress.toFixed(0) }}%</span>
            </div>
          </div>
          
          <div class="details-item" v-if="positionOptions.length > 1">
            <div class="details-label">岗位</div>
            <div class="details-value">{{ taskDetails.positionName || taskDetails.position || '未设置' }}</div>
          </div>
          
          <div class="details-item">
            <div class="details-label">日期</div>
            <div class="details-value">{{ taskDetails.date }}</div>
          </div>
          
          <div class="details-item">
            <div class="details-label">状态</div>
            <div class="details-value">{{ formatTaskStatus(taskDetails.status) }}</div>
          </div>
        </div>
      </div>
    </van-popup>
    
    <!-- 项目/任务选择详情弹窗 -->
    <van-popup v-model="showItemDetails" position="bottom" :style="{ height: '50%' }">
      <div class="task-details">
        <div class="details-header">
          <div class="details-title">{{ currentItemDetails && currentItemDetails.type === 'project' ? '项目详情' : '任务详情' }}</div>
          <div class="details-actions">
            <van-icon name="cross" @click="showItemDetails = false" />
          </div>
        </div>
        
        <div class="details-content" v-if="currentItemDetails">
          <!-- 项目详情 -->
          <template v-if="currentItemDetails.type === 'project'">
            <div class="details-item">
              <div class="details-label">项目名称</div>
              <div class="details-value">{{ currentItemDetails.item.text }}</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">项目描述</div>
              <div class="details-value">{{ currentItemDetails.item.description }}</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">项目经理</div>
              <div class="details-value">{{ currentItemDetails.item.manager }}</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">计划工时总计</div>
              <div class="details-value">{{ calculateProjectTotalPlannedHours(currentItemDetails.item.value) }} 小时</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">已报工时总计</div>
              <div class="details-value">{{ calculateProjectTotalReportedHours(currentItemDetails.item.value) }} 小时</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">整体进度</div>
              <div class="details-value">
                <div class="progress-bar-container">
                  <div class="progress-bar" :style="{ width: `${calculateProjectProgress(currentItemDetails.item.value)}%` }"></div>
                </div>
                <span class="progress-text">{{ calculateProjectProgress(currentItemDetails.item.value).toFixed(0) }}%</span>
              </div>
            </div>
            
            <div class="details-item">
              <div class="details-label">开始日期</div>
              <div class="details-value">{{ currentItemDetails.item.startDate || '未设置' }}</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">结束日期</div>
              <div class="details-value">{{ currentItemDetails.item.endDate || '未设置' }}</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">相关任务</div>
              <div class="details-value task-list-in-details">
                <div 
                  v-for="task in projectTaskMap[currentItemDetails.item.value]" 
                  :key="task.value"
                  class="task-in-details"
                  @click="viewTaskItemDetails(task)"
                >
                  {{ task.text }}
                </div>
              </div>
            </div>
          </template>
          
          <!-- 任务详情 -->
          <template v-else>
            <div class="details-item">
              <div class="details-label">任务名称</div>
              <div class="details-value">{{ currentItemDetails.item.text }}</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">任务类型</div>
              <div class="details-value">
                <span v-if="currentItemDetails.item.taskType === 'agile'" class="agile-task-tag">敏捷任务</span>
                <span v-else>常规任务</span>
              </div>
            </div>
            
            <!-- 添加FOWNPROJECT相关信息到任务详情选择器中 -->
            <div class="details-item" v-if="currentItemDetails.item.ownProject">
              <div class="details-label">所属项目关联</div>
              <div class="details-value">
                <div class="fownproject-info">
                  <div>
                    <span class="info-label"></span>
                    <span class="info-value">{{ currentItemDetails.item.ownProject }}</span>
                  </div>
                  <div v-if="currentItemDetails.projectName && currentItemDetails.item.ownProject !== currentItemDetails.projectName" class="mapping-warning">
                    <van-icon name="warning-o" color="#ff976a" />
                    <span>所属项目与当前项目不一致</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="details-item">
              <div class="details-label">任务描述</div>
              <div class="details-value">{{ currentItemDetails.item.description }}</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">预估工时</div>
              <div class="details-value">{{ currentItemDetails.item.estimatedHours }} 小时</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">优先级</div>
              <div class="details-value">{{ currentItemDetails.item.priority }}</div>
            </div>
            
            <div class="details-item">
              <div class="details-label">所属项目</div>
              <div class="details-value">{{ currentItemDetails.projectName }}</div>
            </div>
          </template>
          
          <div class="details-actions-footer">
            <van-button 
              type="primary" 
              block 
              @click="selectItemFromDetails"
            >选择{{ currentItemDetails && currentItemDetails.type === 'project' ? '此项目' : '此任务' }}</van-button>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 项目/任务统一选择器弹窗 -->
    <van-popup v-model="showCombinedPicker" position="bottom" :style="{ height: '70%' }">
      <div class="combined-picker">
        <div class="picker-header">
          <div class="cancel" @click="cancelCombinedPicker">取消</div>
          <div class="title">
            <div class="main-title">{{ isAddingNewTask ? '选择项目/任务' : '选择项目/任务' }}</div>
          </div>
          <div class="header-actions">
            <div class="confirm" @click="confirmCombinedPicker">确定</div>
          </div>
        </div>
        
        <div class="picker-search">
          <div class="search-with-dropdown">
            <div class="custom-dropdown" @click="toggleSearchTypeDropdown">
              <span class="dropdown-text">{{ searchTypeText }}</span>
              <van-icon name="arrow-down" class="dropdown-arrow" :class="{ 'dropdown-open': showSearchTypeDropdown }" />
              
              <!-- 自定义下拉选项 -->
              <div class="dropdown-options" v-show="showSearchTypeDropdown">
                <div 
                  v-for="option in searchTypeOptions" 
                  :key="option.value"
                  class="dropdown-option"
                  :class="{ active: searchType === option.value }"
                  @click.stop="selectSearchType(option)"
                >
                  {{ option.text }}
                </div>
              </div>
            </div>
            <van-search
              v-model="searchKeyword"
              :placeholder="searchPlaceholder"
              shape="round"
              background="transparent"
              clearable
            />
          </div>
        </div>
        
        <div class="picker-content">
          <div 
            v-for="item in combinedOptions" 
            :key="`${item.itemType}-${item.value}`"
            class="picker-item-wrapper"
            :class="{ 'has-active-item': isItemSelected(item) }"
          >
            <div 
              class="picker-item"
              :class="{ 
                active: isItemSelected(item),
                'project-item': item.itemType === 'project',
                'task-item': item.itemType === 'task',
                'special-item': item.itemType === 'special',
                'temp-task-item': item.isSpecial && item.value === 'temp-task',
                'time-cost-item': item.isSpecial && item.value === 'time-cost',
                'multi-select-mode': isMultiSelectMode
              }"
              @click="handleItemClick(item)"
            >
              <!-- 多选模式下显示复选框（包括临时任务和耗费任务） -->
              <div v-if="isMultiSelectMode && (!item.isSpecial || item.value === 'temp-task' || item.value === 'time-cost')" class="item-checkbox">
                <van-checkbox :value="isItemSelected(item)" @click.stop="toggleItemSelection(item)" />
              </div>
              
              <div class="item-content">
                <div class="item-title">{{ item.displayText }}</div>
                <div class="item-desc">{{ item.description }}</div>
                <div v-if="item.itemType === 'task' && !taskForm.project" class="item-project">
                  所属项目: {{ item.projectName }}
                </div>
              </div>
              <div class="item-type">
                <div class="type-tag" :class="item.itemType">
                  {{ 
                    item.itemType === 'special' ? 
                      (item.value === 'temp-task' ? '临时任务' : '耗费记录') :
                      (item.itemType === 'project' ? '项目' : (item.taskType === 'agile' ? '敏捷任务' : '任务'))
                  }}
                </div>
                <van-icon 
                  v-if="!item.isSpecial"
                  :name="isItemExpanded(item) ? 'arrow-up' : 'arrow-down'" 
                  class="expand-icon" 
                  @click.stop="toggleItemExpanded(item)" 
                />
              </div>
            </div>

            <!-- 展开的详情区域 -->
            <div v-if="isItemExpanded(item)" class="item-details-expanded">
              <!-- 项目详情 -->
              <template v-if="item.itemType === 'project'">
                <div class="detail-row">
                  <span class="detail-label">所属项目:</span>
                  <span class="detail-value">{{ item.text }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">计划工时:</span>
                  <span class="detail-value">{{ calculateProjectTotalPlannedHours(item.value) || 0 }} 小时</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">已报工时:</span>
                  <span class="detail-value">{{ calculateProjectTotalReportedHours(item.value) || 0 }} 小时</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">进度:</span>
                  <span class="detail-value">
                    <div class="progress-display">
                      <div class="progress-bar-mini">
                        <div class="progress-fill" :style="{ width: `${calculateProjectProgress(item.value)}%` }"></div>
                      </div>
                      <span class="progress-text">{{ calculateProjectProgress(item.value).toFixed(0) }}%</span>
                    </div>
                  </span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">计划开始时间:</span>
                  <span class="detail-value">{{ item.startDate || '未设置' }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">计划结束时间:</span>
                  <span class="detail-value">{{ item.endDate || '未设置' }}</span>
                </div>
              </template>

              <!-- 任务详情 -->
              <template v-else>
                <div class="detail-row">
                  <span class="detail-label">所属项目:</span>
                  <span class="detail-value">{{ item.projectName || '未设置' }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">计划工时:</span>
                  <span class="detail-value">{{ item.plannedHours || item.estimatedHours || 0 }} 小时</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">已报工时:</span>
                  <span class="detail-value">{{ getTaskReportedHours(item) }} 小时</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">进度:</span>
                  <span class="detail-value">
                    <div class="progress-display">
                      <div class="progress-bar-mini">
                        <div class="progress-fill" :style="{ width: `${getTaskProgress(item)}%` }"></div>
                      </div>
                      <span class="progress-text">{{ getTaskProgress(item).toFixed(0) }}%</span>
                    </div>
                  </span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">计划开始时间:</span>
                  <span class="detail-value">{{ item.plannedStartDate || item.startDate || '未设置' }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">计划结束时间:</span>
                  <span class="detail-value">{{ item.plannedEndDate || item.endDate || '未设置' }}</span>
                </div>
              </template>
            </div>
          </div>
          
          <div v-if="combinedOptions.length === 0" class="no-results">
            <van-empty description="没有匹配的结果" />
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 进度选择弹窗 -->
    <van-popup v-model="showProgressPicker" position="bottom">
      <van-picker
        show-toolbar
        title="选择进度"
        :columns="progressOptions"
        value-key="text"
        @confirm="onProgressConfirm"
        @cancel="showProgressPicker = false"
      />
    </van-popup>

    <!-- 内联进度选择器弹窗 -->
    <van-popup v-model="showInlineProgressPicker" position="bottom">
      <van-picker
        show-toolbar
        title="选择进度"
        :columns="progressOptions"
        value-key="text"
        @confirm="onInlineProgressConfirm"
        @cancel="cancelInlineProgressEdit"
      />
    </van-popup>

    <!-- 内联岗位选择器弹窗 -->
    <van-popup v-model="showInlinePositionPicker" position="bottom" v-if="positionOptions.length > 1">
      <van-picker
        show-toolbar
        title="选择岗位"
        :columns="positionOptions"
        value-key="text"
        @confirm="onInlinePositionConfirm"
        @cancel="cancelInlinePositionEdit"
      />
    </van-popup>

    <!-- 内联临时任务类型选择器弹窗 -->
    <van-popup v-model="showInlineTmpTypePicker" position="bottom">
      <van-picker
        show-toolbar
        title="选择临时任务类型"
        :columns="tmpTaskTypeOptions"
        value-key="text"
        @confirm="onInlineTmpTypeConfirm"
        @cancel="cancelInlineTmpTypeEdit"
      />
    </van-popup>

    <!-- 内联耗费类型选择器弹窗 -->
    <van-popup v-model="showInlineBurnOffTypePicker" position="bottom">
      <van-picker
        show-toolbar
        title="选择耗费类型"
        :columns="burnOffTypeOptions"
        value-key="text"
        @confirm="onInlineBurnOffTypeConfirm"
        @cancel="cancelInlineBurnOffTypeEdit"
      />
    </van-popup>
  </div>
</template>

<script>
import BackButton from '../components/BackButton.vue';
import {
  getWorkReportData,
  saveWorkReportData,
  submitWorkReportData,
  checkApiAvailability,
  getProjectAndTaskData,
  getPositionData, // 添加这一行
  getBurnOffTypeData, // 添加耗费类型数据API
  getTmpTaskTypeData, // 添加临时任务类型数据API
  deleteWorkReportData, // 添加删除API导入
  undoWorkReportData, // 添加撤销API导入
  getTodayTasksData, // 添加今日任务API导入
  getUserDueProjectTasks, // 添加已报工时API导入
  getDueProjectTasks // 添加任务详情专用已报工时API导入
} from '../api/report';
import WorkContentEditor from '@/components/WorkContentEditor.vue';

export default {
  name: 'WorkReport',
  components: {
    BackButton,
    WorkContentEditor
  },
  data() {
    return {
      // 标签页
      activeTab: 0,
      
      // 日历相关
      calendarMode: 'week', // 'week', 'month', 'year'
      currentDate: new Date(),
      selectedDate: new Date(),
      weekDays: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      
      // 任务相关
      tasks: [],
      showTaskForm: false,
      isEditingTask: false,
      isAddingNewTask: false, // 添加新任务模式标记
      currentTaskId: null,
      // 内联编辑相关属性（卡片模式使用）
      editingTaskId: null, // 当前正在编辑的任务ID
      editingField: null, // 当前正在编辑的字段
      // 项目分组展开状态管理
      projectGroupStates: {}, // 记录每个项目的展开/收起状态，默认为展开
      // 添加数据更新标志，用于防止重复更新
      isUpdatingData: false,
      // 缓存控制标志（已禁用缓存，每次都实时获取数据）
      cacheNeedsRefresh: false,
      taskForm: {
        taskName: '',
        taskCode: '',
        taskContent: '',
        project: '',
        projectName: '',
        task: '',
        regularHours: 0,
        overtimeHours: 0,
        progress: 0,
        date: this.formatDate(new Date()),
        isProjectLevel: false,
        plannedHours: 0,
        startDate: '',
        endDate: '',
        plannedStartDate: '', // 添加计划开始日期字段
        plannedEndDate: '',   // 添加计划结束日期字段
        position: '',        // 保留原字段用于存储岗位编号
        positionName: '',     // 新增字段用于存储岗位名称
        allSearchResults: [] // 存储所有搜索结果
      },
      
      // 任务详情相关
      showTaskDetails: false,
      taskDetails: null,
      
      // 选择器
      showProjectPicker: false,
      showTaskNamePicker: false,
      showPositionPicker: false,
      showInlinePositionPicker: false, // 内联岗位选择器
      showProgressPicker: false, // 进度选择器
      showInlineProgressPicker: false, // 内联进度选择器
      showInlineTmpTypePicker: false, // 内联临时任务类型选择器
      showInlineBurnOffTypePicker: false, // 内联耗费类型选择器
      
      projectOptions: [],
      
      // 任务名称选择器
      taskNameOptions: [], // 当前可选的任务名称列表
      
      // 项目任务映射关系
      projectTaskMap: {},
      
      // 详情弹窗
      showItemDetails: false,
      currentItemDetails: null,
      
      // 统一选择器
      showCombinedPicker: false,
      searchKeyword: '',
      filterType: 'all', // 'all', 'project', 'task'
      searchType: 'all', // 'all', 'project', 'task' - 新增
      
      // 多选模式相关
      isMultiSelectMode: false, // 是否为多选模式
      selectedItems: [], // 多选模式下选中的项目数组
      
      // 日期选择器
      showStartDatePicker: false,
      showEndDatePicker: false,
      
      // 岗位选项
      positionOptions: [
        { text: '开发工程师', value: '开发工程师' },
        { text: '测试工程师', value: '测试工程师' },
        { text: '产品经理', value: '产品经理' },
        { text: '项目经理', value: '项目经理' },
        { text: '设计师', value: '设计师' },
        { text: '运维工程师', value: '运维工程师' },
        { text: '技术支持', value: '技术支持' },
        { text: '行政人员', value: '行政人员' }
      ],
      
      // 进度选项
      progressOptions: [
        { text: '10%', value: 10 },
        { text: '20%', value: 20 },
        { text: '30%', value: 30 },
        { text: '40%', value: 40 },
        { text: '50%', value: 50 },
        { text: '60%', value: 60 },
        { text: '70%', value: 70 },
        { text: '80%', value: 80 },
        { text: '90%', value: 90 },
        { text: '100%', value: 100 }
      ],
      
      // 临时任务类型选项
      tmpTaskTypeOptions: [],
      
      // 耗费类型选项
      burnOffTypeOptions: [],
      
      // 添加开发环境标志
      isDevelopment: process.env.NODE_ENV === 'development',
      
      // 新增：导航相关
      navigating: false,
      
      // 新增：日期状态映射
      dateStatusMap: {},
      
      // 触摸滑动相关
      touchStartX: 0,
      touchStartY: 0,
      touchStartTime: 0,
      isSwiping: false,
      swipeThreshold: 50, // 最小滑动距离
      // 搜索类型选择器
      showSearchTypePicker: false,
      searchTypeText: '全部',
      showSearchTypeDropdown: false, // 自定义下拉菜单显示状态
      
      // 项目/任务详情展开状态管理
      expandedItemDetails: {}, // 记录每个项目/任务的展开状态
      
      // 法定节假日列表
      holidayList: [
        // 2024年法定节假日
        '2024-01-01', // 元旦
        '2024-02-10', '2024-02-11', '2024-02-12', '2024-02-13', '2024-02-14', '2024-02-15', '2024-02-16', '2024-02-17', // 春节
        '2024-04-04', '2024-04-05', '2024-04-06', // 清明节
        '2024-05-01', '2024-05-02', '2024-05-03', // 劳动节
        '2024-06-10', // 端午节
        '2024-09-15', '2024-09-16', '2024-09-17', // 中秋节
        '2024-10-01', '2024-10-02', '2024-10-03', '2024-10-04', '2024-10-05', '2024-10-06', '2024-10-07', // 国庆节
        // 2025年法定节假日
        '2025-01-01', // 元旦
        '2025-01-28', '2025-01-29', '2025-01-30', '2025-01-31', '2025-02-01', '2025-02-02', '2025-02-03', // 春节
        '2025-04-05', '2025-04-06', '2025-04-07', // 清明节
        '2025-05-01', '2025-05-02', '2025-05-03', // 劳动节
        '2025-05-31', // 端午节
        '2025-10-01', '2025-10-02', '2025-10-03', '2025-10-04', '2025-10-05', '2025-10-06', '2025-10-07', // 国庆节
        '2025-10-06' // 中秋节（与国庆重合）
      ],
      
      // 表格模式相关
      tableHeaderHeight: 60,
      isSaving: false, // 添加保存状态标记
      lastSaveTime: 0, // 添加最后保存时间记录

      // 已报工时数据缓存
      reportedHoursCache: {}, // 缓存已报工时数据，格式：{taskId: reportedHours}

      // 任务详情专用已报工时
      taskDetailsReportedHours: 0, // 任务详情弹窗中显示的已报工时
      taskDetailsReportedHoursLoading: false, // 任务详情已报工时加载状态
      taskDetailsReportedHoursError: null, // 任务详情已报工时错误信息

      // 防抖相关
      reportedHoursDebounceTimers: {}, // 防抖定时器
      pendingReportedHoursRequests: new Set() // 正在进行的请求集合
    };
  },
  computed: {
    calendarTitle() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth() + 1;
      
      if (this.calendarMode === 'week') {
        return `${year}年${month}月`;
      } else if (this.calendarMode === 'month') {
        return `${year}年${month}月`;
      } else {
        return `${year}年`;
      }
    },
    formattedSelectedDate() {
      const year = this.selectedDate.getFullYear();
      const month = this.selectedDate.getMonth() + 1;
      const day = this.selectedDate.getDate();
      return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    },
    // 合并项目和任务的列表，用于统一选择器
    combinedOptions() {
      // 所有项目
      const projects = this.projectOptions.map(project => ({
        ...project,
        itemType: 'project',
        displayText: project.text,
        searchText: project.text + ' ' + project.description
      }));
      
      // 根据当前选择的项目过滤任务
      let tasks = [];
      if (this.taskForm.project) {
        // 如果已选择项目，只显示该项目下的任务
        const projectTasks = this.projectTaskMap[this.taskForm.project] || [];
        tasks = projectTasks.map(task => ({
          ...task,
          itemType: 'task',
          projectValue: this.taskForm.project,
          displayText: task.text,
          searchText: task.text + ' ' + task.description,
        }));
      } else {
        // 如果未选择项目，显示所有任务
        Object.entries(this.projectTaskMap).forEach(([projectValue, projectTasks]) => {
          const projectName = this.projectOptions.find(p => p.value === projectValue)?.text || projectValue;
          const tasksWithProject = projectTasks.map(task => ({
            ...task,
            itemType: 'task',
            projectValue,
            projectName,
            displayText: task.text,
            searchText: task.text + ' ' + task.description + ' ' + projectName
          }));
          tasks = [...tasks, ...tasksWithProject];
        });
      }
      
      // 根据搜索关键词过滤
      const filteredItems = [...projects, ...tasks].filter(item => {
        if (!this.searchKeyword) return true;
        return item.searchText.toLowerCase().includes(this.searchKeyword.toLowerCase());
      });
      
      // 根据过滤类型进行过滤
      let finalItems = [];
      if (this.filterType === 'project') {
        finalItems = filteredItems.filter(item => item.itemType === 'project');
      } else if (this.filterType === 'task') {
        finalItems = filteredItems.filter(item => item.itemType === 'task');
      } else {
        finalItems = filteredItems;
      }
      
      // 在末尾添加临时任务和耗费特殊选项（仅在未搜索时显示）
      if (!this.searchKeyword) {
        finalItems.push(
          {
            itemType: 'special',
            value: 'temp-task',
            displayText: '临时任务',
            description: '创建临时任务记录',
            searchText: '临时任务',
            isSpecial: true
          },
          {
            itemType: 'special',
            value: 'time-cost',
            displayText: '耗费',
            description: '记录时间耗费',
            searchText: '耗费',
            isSpecial: true
          }
        );
      }
      
      return finalItems;
    },
    calendarDays() {
      // 根据当前日历模式生成天数数组
      if (this.calendarMode === 'week') {
        return this.getWeekDays();
      } else if (this.calendarMode === 'month') {
        return this.getMonthDays();
      } else {
        // 年视图简化处理，只显示当前月
        return this.getMonthDays();
      }
    },
    yearMonths() {
      const year = this.currentDate.getFullYear();
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      
      return Array.from({ length: 12 }, (_, i) => ({
        index: i,
        name: `${i + 1}月`,
        isCurrent: i === currentMonth && year === currentYear
      }));
    },
    searchTypeOptions() {
      return [
        { text: '全部', value: 'all' },
        { text: '项目', value: 'project' },
        { text: '任务', value: 'task' }
      ];
    },
    searchPlaceholder() {
      if (this.searchType === 'project') {
        return '搜索项目';
      } else if (this.searchType === 'task') {
        return '搜索任务';
      } else {
        return '搜索项目或任务';
      }
    },
    
    // 日期选择器的计算属性
    startDateValue() {
      if (!this.taskForm.startDate) return null;
      const [year, month, day] = this.taskForm.startDate.split('-').map(Number);
      return new Date(year, month - 1, day);
    },
    
    endDateValue() {
      if (!this.taskForm.endDate) return null;
      const [year, month, day] = this.taskForm.endDate.split('-').map(Number);
      return new Date(year, month - 1, day);
    },
    
    // 已报工时总计
    totalReportedHours() {
      return this.taskForm.regularHours + this.taskForm.overtimeHours;
    },
    // 添加任务状态统计计算属性
    taskStatusStats() {
      // 初始化统计数据
      const counts = {
        draft: 0,     // 暂存 (状态代码 'a'/'A' 和 'd'/'D')
        submitted: 0, // 已提交 (状态代码 'b'/'B')
        approved: 0   // 已审核 (状态代码 'c'/'C')
      };
      
      // 遍历任务数组，根据status统计工时数量
      this.tasks.forEach(task => {
        if (!task.status) return;
        
        // 计算当前任务的总工时（汇报工时 + 加班工时）
        const totalHours = (parseFloat(task.regularHours || 0) + parseFloat(task.overtimeHours || 0));
        
        // 转换为小写进行比较
        const lowerStatus = task.status.toLowerCase();
        
        if (lowerStatus === 'a' || lowerStatus === 'd') {
          counts.draft += totalHours; // 将重新审核状态合并到暂存统计
        } else if (lowerStatus === 'b') {
          counts.submitted += totalHours;
        } else if (lowerStatus === 'c') {
          counts.approved += totalHours;
        }
      });
      
      // 返回格式化的统计结果（智能格式化）
      return `${this.formatNumber(counts.draft)}/${this.formatNumber(counts.submitted)}/${this.formatNumber(counts.approved)}`;
    },
    
    // 表格编辑相关计算属性
    isTableMode() {
      return this.displayMode === 'table';
    },
    
    isCardMode() {
      return this.displayMode === 'card';
    },
    
    // 格式化表格数据
    tableData() {
      return this.tasks.map(task => ({
        ...task,
        projectDisplay: task.projectName || task.project || '未设置',
        taskDisplay: task.isProjectLevel ? '项目工作' : (task.taskName || '未设置'),
        statusDisplay: this.formatTaskStatus(task.status),
        progressDisplay: `${(task.progress || 0).toFixed(0)}%`,
        positionDisplay: task.positionName || task.position || '未设置',
        totalHours: (parseFloat(task.regularHours || 0) + parseFloat(task.overtimeHours || 0)).toFixed(1)
      }));
    },
    
    // 按项目分组的任务数据
    groupedTasks() {
      const groups = {};
      
      // 按项目分组
      this.tasks.forEach(task => {
        const projectKey = task.projectName || task.project || '未分配项目';
        
        if (!groups[projectKey]) {
          groups[projectKey] = {
            projectName: projectKey,
            tasks: [],
            totalTasks: 0,
            totalRegularHours: 0,
            totalOvertimeHours: 0,
            isExpanded: this.projectGroupStates[projectKey] !== false, // 默认展开，除非明确设置为false
            statusCounts: { draft: 0, submitted: 0, approved: 0 }
          };
        }
        
        groups[projectKey].tasks.push(task);
        groups[projectKey].totalTasks++;
        groups[projectKey].totalRegularHours += parseFloat(task.regularHours || 0);
        groups[projectKey].totalOvertimeHours += parseFloat(task.overtimeHours || 0);
        
        // 统计任务状态
        const status = (task.status || '').toLowerCase();
        if (status === 'a' || status === 'd') {
          groups[projectKey].statusCounts.draft++; // 将重新审核状态合并到暂存统计
        } else if (status === 'b') {
          groups[projectKey].statusCounts.submitted++;
        } else if (status === 'c') {
          groups[projectKey].statusCounts.approved++;
        }
      });
      
      // 转换为数组并排序
      return Object.values(groups).sort((a, b) => a.projectName.localeCompare(b.projectName));
    },
    // 过滤后的任务分组
    filteredGroupedTasks() {
      return this.groupedTasks;
    }
  },
  mounted() {
    console.log('[WorkReport] 组件已挂载，开始初始化');
    
    // 恢复用户的视图偏好
    const savedDisplayMode = localStorage.getItem('workReportDisplayMode');
    if (savedDisplayMode && ['table', 'card'].includes(savedDisplayMode)) {
      this.displayMode = savedDisplayMode;
      console.log(`[WorkReport] 恢复用户视图偏好: ${this.displayMode}`);
    }
    
    // 添加点击外部关闭下拉菜单的事件监听器
    document.addEventListener('click', this.handleClickOutside);
    
    // 初始化数据
    this.loadTasks();
    // 加载项目和任务数据
    this.loadProjectAndTaskData();
    // 加载岗位数据
    this.loadPositionData();
    // 加载耗费类型数据
    this.loadBurnOffTypeData();
    // 加载临时任务类型数据
    this.loadTmpTaskTypeData();
    
    // 延迟加载日历状态数据，确保组件完全初始化
    this.$nextTick(async () => {
      console.log('[WorkReport] 开始加载日历状态数据');
      await this.refreshCalendarStatus();
    });
  },
  beforeDestroy() {
    // 移除点击外部关闭下拉菜单的事件监听器
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    // 任务去重方法：根据任务名称去重，保留最新的任务
    deduplicateTasksByName(tasks) {
      if (!tasks || tasks.length === 0) return [];

      const taskMap = new Map();
      const duplicateCount = {};

      // 统计重复任务
      tasks.forEach(task => {
        const taskName = task.taskName || task.projectName || '';
        if (taskName && taskName.trim() !== '') {
          duplicateCount[taskName] = (duplicateCount[taskName] || 0) + 1;
        }
      });

      // 记录重复的任务名称
      const duplicateNames = Object.keys(duplicateCount).filter(name => duplicateCount[name] > 1);
      if (duplicateNames.length > 0) {
        console.log(`[WorkReport] 发现重复任务: ${duplicateNames.join(', ')}`);
      }

      // 去重处理：对于相同名称的任务，保留最新的（ID最大的）
      tasks.forEach(task => {
        const taskName = task.taskName || task.projectName || '';

        if (!taskName || taskName.trim() === '') {
          // 如果任务名称为空，直接添加（使用随机key避免覆盖）
          const randomKey = `empty_${task.id || Date.now()}_${Math.random()}`;
          taskMap.set(randomKey, task);
          return;
        }

        const existingTask = taskMap.get(taskName);
        if (!existingTask) {
          // 第一次遇到这个任务名称
          taskMap.set(taskName, task);
        } else {
          // 已存在相同名称的任务，比较ID保留最新的
          const currentTaskId = parseInt(task.id) || 0;
          const existingTaskId = parseInt(existingTask.id) || 0;

          if (currentTaskId > existingTaskId) {
            console.log(`[WorkReport] 替换重复任务 "${taskName}": ${existingTask.id} -> ${task.id}`);
            taskMap.set(taskName, task);
          } else {
            console.log(`[WorkReport] 保留原任务 "${taskName}": ${existingTask.id} (跳过 ${task.id})`);
          }
        }
      });

      const uniqueTasks = Array.from(taskMap.values());
      const removedCount = tasks.length - uniqueTasks.length;

      if (removedCount > 0) {
        console.log(`[WorkReport] 任务去重完成: 移除${removedCount}个重复任务，保留${uniqueTasks.length}个唯一任务`);
      }

      return uniqueTasks;
    },

    // 获取任务的真实已报工时
    async getTaskRealReportedHours(task) {
      if (!task) return 0;

      // 优先使用任务对象中的 fsumtime 字段（已报工时）
      if (task.fsumtime !== undefined && task.fsumtime > 0) {
        const reportedHours = parseFloat(task.fsumtime) || 0;
        console.log('[DEBUG] 使用任务对象中的已报工时 (fsumtime):', reportedHours, '任务:', task.taskName);

        // 将结果缓存，避免重复计算
        const taskOrProjectId = task.taskId || task.taskCode || task.projectId || task.project;
        if (taskOrProjectId) {
          const cacheKey = `${taskOrProjectId}`;
          this.$set(this.reportedHoursCache, cacheKey, reportedHours);
        }

        return reportedHours;
      }

      // 调试：打印任务对象结构
      console.log('[DEBUG] 任务对象结构:', {
        id: task.id,
        taskCode: task.taskCode,
        task: task.task,
        project: task.project,
        taskName: task.taskName,
        projectName: task.projectName,
        fsumtime: task.fsumtime,
        fprojectId: task.fprojectId,
        ftaskId: task.ftaskId,
        allKeys: Object.keys(task)
      });

      // 根据您提供的数据结构，优先使用正确的ID字段
      // 如果 ftaskId > 0，使用 ftaskId（具体任务）
      // 如果 ftaskId = 0，使用 fprojectId（项目级别工作）
      // 如果都没有，回退到原有逻辑
      let taskOrProjectId;
      if (task.ftaskId && task.ftaskId > 0) {
        taskOrProjectId = task.ftaskId;
        console.log('[DEBUG] 使用任务ID (ftaskId):', taskOrProjectId);
      } else if (task.fprojectId) {
        taskOrProjectId = task.fprojectId;
        console.log('[DEBUG] 使用项目ID (fprojectId):', taskOrProjectId);
      } else {
        // 回退到原有逻辑
        taskOrProjectId = task.taskId || task.taskCode || task.projectId || task.project;
        console.log('[DEBUG] 回退到原有逻辑，使用ID:', taskOrProjectId);
      }
      if (!taskOrProjectId) {
        console.warn('[DEBUG] 无法获取任务或项目ID，任务对象:', task);
        return 0;
      }

      console.log('[DEBUG] 使用的任务/项目ID:', taskOrProjectId);

      // 检查缓存
      const cacheKey = `${taskOrProjectId}`;
      if (this.reportedHoursCache[cacheKey] !== undefined) {
        console.log('[DEBUG] 使用缓存的已报工时:', this.reportedHoursCache[cacheKey]);
        return this.reportedHoursCache[cacheKey];
      }

      try {
        console.log('[DEBUG] 调用API获取已报工时，使用ID:', taskOrProjectId, '任务名称:', task.taskName);
        const response = await getUserDueProjectTasks(taskOrProjectId);
        console.log('[DEBUG] 已报工时API响应:', response);

        if (response.success && response.data) {
          const reportedHours = response.data.reportedHours || 0;
          console.log('[DEBUG] ✓ 成功获取已报工时:', reportedHours, '小时，任务:', task.taskName);
          // 缓存结果
          this.$set(this.reportedHoursCache, cacheKey, reportedHours);
          return reportedHours;
        } else {
          console.warn('[DEBUG] ⚠️ 已报工时API返回失败:', response.message, '任务:', task.taskName);
        }
      } catch (error) {
        console.error('[DEBUG] ❌ 获取已报工时失败:', error, '任务:', task.taskName);
      }

      // API失败时直接返回0，不再使用计算值作为后备
      console.log('[DEBUG] API获取已报工时失败，返回0');
      return 0;
    },

    // 获取任务显示的已报工时（同步方法，用于模板显示）
    getTaskDisplayReportedHours(task) {
      if (!task) return 0;

      // 优先使用任务对象中的 fsumtime 字段（已报工时）
      if (task.fsumtime !== undefined && task.fsumtime > 0) {
        console.log('[DEBUG] 使用任务对象中的已报工时 (fsumtime):', task.fsumtime, '任务:', task.taskName);
        return parseFloat(task.fsumtime) || 0;
      }

      // 使用与 getTaskRealReportedHours 相同的ID选择逻辑
      let taskOrProjectId;
      if (task.ftaskId && task.ftaskId > 0) {
        taskOrProjectId = task.ftaskId;
      } else if (task.fprojectId) {
        taskOrProjectId = task.fprojectId;
      } else {
        // 回退到原有逻辑
        taskOrProjectId = task.taskId || task.taskCode || task.projectId || task.project;
      }

      if (!taskOrProjectId) return 0;

      const cacheKey = `${taskOrProjectId}`;

      // 如果缓存中有数据，使用缓存的真实数据
      if (this.reportedHoursCache[cacheKey] !== undefined) {
        console.log('[DEBUG] 显示缓存的已报工时:', this.reportedHoursCache[cacheKey], '任务ID:', taskOrProjectId);
        return this.reportedHoursCache[cacheKey];
      }

      // 如果没有缓存，异步获取真实数据，同时返回0
      console.log('[DEBUG] 缓存中无数据，异步获取真实数据，暂时返回0，任务:', task.taskName);
      // 使用防抖机制，避免重复请求同一个任务的数据
      this.debouncedGetReportedHours(task);
      return 0;
    },

    // 串行预加载所有任务的已报工时数据（避免并发请求过多）
    async preloadReportedHours() {
      if (!this.tasks || this.tasks.length === 0) return;

      console.log('[WorkReport] 开始串行预加载已报工时数据，任务数量:', this.tasks.length);

      let successCount = 0;
      let failureCount = 0;
      const startTime = Date.now();

      // 串行处理每个任务，避免并发请求过多
      for (let i = 0; i < this.tasks.length; i++) {
        const task = this.tasks[i];

        try {
          console.log(`[WorkReport] 正在加载第 ${i + 1}/${this.tasks.length} 个任务的已报工时: ${task.taskName}`);
          await this.getTaskRealReportedHours(task);
          successCount++;

          // 添加小延迟，避免请求过于频繁
          if (i < this.tasks.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100)); // 100ms延迟
          }
        } catch (error) {
          console.warn(`[WorkReport] 预加载任务 ${task.id} 的已报工时失败:`, error);
          failureCount++;
        }
      }

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;

      console.log(`[WorkReport] 已报工时数据预加载完成，耗时: ${duration.toFixed(2)}秒`);
      console.log(`[WorkReport] 成功: ${successCount}, 失败: ${failureCount}, 缓存数量: ${Object.keys(this.reportedHoursCache).length}`);

      // 强制更新视图以显示最新的已报工时数据
      this.$forceUpdate();
    },

    // 智能预加载：只加载当前可见或即将可见的任务的已报工时
    async smartPreloadReportedHours() {
      if (!this.tasks || this.tasks.length === 0) return;

      // 限制预加载数量，避免一次性加载太多
      const maxPreloadCount = 10; // 最多预加载10个任务
      const tasksToPreload = this.tasks.slice(0, maxPreloadCount);

      console.log(`[WorkReport] 开始智能预加载已报工时数据，预加载数量: ${tasksToPreload.length}/${this.tasks.length}`);

      let successCount = 0;
      let failureCount = 0;

      // 串行处理预加载的任务
      for (let i = 0; i < tasksToPreload.length; i++) {
        const task = tasksToPreload[i];

        // 检查是否已经有缓存，如果有就跳过
        const taskOrProjectId = this.getTaskOrProjectId(task);
        const cacheKey = `${taskOrProjectId}`;

        if (this.reportedHoursCache[cacheKey] !== undefined) {
          console.log(`[WorkReport] 跳过已缓存的任务: ${task.taskName}`);
          continue;
        }

        try {
          await this.getTaskRealReportedHours(task);
          successCount++;

          // 添加小延迟
          if (i < tasksToPreload.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 150)); // 150ms延迟
          }
        } catch (error) {
          console.warn(`[WorkReport] 智能预加载任务 ${task.id} 的已报工时失败:`, error);
          failureCount++;
        }
      }

      console.log(`[WorkReport] 智能预加载完成，成功: ${successCount}, 失败: ${failureCount}`);

      // 更新视图
      this.$forceUpdate();
    },

    // 获取任务或项目ID的辅助方法
    getTaskOrProjectId(task) {
      if (task.ftaskId && task.ftaskId > 0) {
        return task.ftaskId;
      } else if (task.fprojectId) {
        return task.fprojectId;
      } else {
        return task.taskId || task.taskCode || task.projectId || task.project;
      }
    },

    // 防抖获取已报工时数据，避免重复请求
    debouncedGetReportedHours(task) {
      const taskOrProjectId = this.getTaskOrProjectId(task);
      if (!taskOrProjectId) return;

      const cacheKey = `${taskOrProjectId}`;

      // 如果已经在请求中，直接返回
      if (this.pendingReportedHoursRequests.has(cacheKey)) {
        console.log('[DEBUG] 已报工时请求正在进行中，跳过:', task.taskName);
        return;
      }

      // 清除之前的防抖定时器
      if (this.reportedHoursDebounceTimers[cacheKey]) {
        clearTimeout(this.reportedHoursDebounceTimers[cacheKey]);
      }

      // 设置新的防抖定时器
      this.reportedHoursDebounceTimers[cacheKey] = setTimeout(async () => {
        try {
          console.log('[DEBUG] 防抖触发，开始获取已报工时:', task.taskName);
          this.pendingReportedHoursRequests.add(cacheKey);
          await this.getTaskRealReportedHours(task);
        } catch (error) {
          console.error('[DEBUG] 防抖获取已报工时失败:', error, task.taskName);
        } finally {
          this.pendingReportedHoursRequests.delete(cacheKey);
          delete this.reportedHoursDebounceTimers[cacheKey];
        }
      }, 300); // 300ms防抖延迟
    },

    // 滚动到输入框位置，避免键盘遮挡
    scrollToInput(inputElement) {
      try {
        if (!inputElement) return;
        
        // 获取输入框的位置信息
        const rect = inputElement.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        
        // 计算输入框底部距离窗口顶部的距离
        const inputBottom = rect.bottom;
        
        // 如果输入框底部超出可视区域，需要滚动
        if (inputBottom > windowHeight - 100) { // 留100px的安全距离
          // 计算需要滚动的距离
          const scrollDistance = inputBottom - windowHeight + 150; // 额外留150px空间
          
          // 使用平滑滚动
          window.scrollBy({
            top: scrollDistance,
            behavior: 'smooth'
          });
          
          console.log(`[WorkReport] 已滚动 ${scrollDistance}px 以避免键盘遮挡`);
        }
      } catch (error) {
        console.warn('[WorkReport] 滚动到输入框位置时出错:', error);
      }
    },
    
    /**
     * 智能格式化数字，整数不显示小数点，有小数则显示
     * @param {number} value 要格式化的数值
     * @returns {string} 格式化后的字符串
     */
    formatNumber(value) {
      // 确保输入是数字
      const num = parseFloat(value) || 0;
      
      // 检查是否为整数
      if (Number.isInteger(num)) {
        // 整数直接转为字符串，不显示小数点
        return num.toString();
      } else {
        // 有小数部分，保留一位小数
        return num.toFixed(1);
      }
    },
    
    /**
     * 下拉刷新数据方法
     * 由refreshMixin调用，实现页面特定的刷新逻辑
     */
    async refreshData() {
      console.log('[WorkReport] 开始下拉刷新数据');
      
      try {
        // 清除缓存，确保获取最新数据
        this.clearCache();
        
        // 重新加载任务数据
        await this.loadTasks(true); // 传递true参数表示强制刷新
        
        // 重新加载项目和任务数据
        await this.loadProjectAndTaskData();
        
        // 重新加载岗位数据
        await this.loadPositionData();
        
        // 刷新日历状态数据
        await this.refreshCalendarStatus();
        
        console.log('[WorkReport] 下拉刷新完成');
        
      } catch (error) {
        console.error('[WorkReport] 下拉刷新失败:', error);
        throw error; // 重新抛出错误，让混入处理
      }
    },
    
    // 添加测试方法，用于直接测试项目和任务数据API
    testFetchProjectData() {
      console.log('[TEST] 开始直接测试获取项目和任务数据API');
      
      // 获取用户ID
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const userId = userInfo.userEntityId || userInfo.userId; // 优先使用FUserId，后备使用Fid
      
      // 添加日志以便验证使用的是哪个ID
      console.log('[TEST] 用户ID信息 - userEntityId:', userInfo.userEntityId, 'userId:', userInfo.userId, '最终使用的userId:', userId);
      
      if (!userId) {
        console.error('[TEST] 无法获取用户ID');
        return;
      }
      
      console.log(`[TEST] 用户ID: ${userId}`);
      
      // 构建请求数据
      const requestData = {
        parameter: {
          data: {
            userId: userId
          }
        }
      };
      
      console.log('[TEST] 请求参数:', requestData);
      
      // 使用fetch API发送请求
      fetch('http://140.249.162.74:81/K3Cloudxm/JR.K3.PLM.WorkhourReport.CustomWebapi.GetProjectAndTask.Get,JR.K3.PLM.WorkhourReport.common.kdsvc', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })
      .then(response => response.json())
      .then(data => {
        console.log('[TEST] 获取到的原始响应:', data);
        
        // 分析响应结构
        if (data && data.data && Array.isArray(data.data)) {
          console.log('[TEST] 响应中包含嵌套的data数组，长度:', data.data.length);
          
          // 分析第一条记录
          if (data.data.length > 0) {
            const firstItem = data.data[0];
            console.log('[TEST] 第一条记录的字段:', Object.keys(firstItem).join(', '));
            console.log('[TEST] 第一条记录详情:', firstItem);
            
            // 检查是否包含类别字段
            if (firstItem.FCATEGORY) {
              console.log('[TEST] 记录类别:', firstItem.FCATEGORY);
            }
            
            // 检查关联字段
            if (firstItem.FCATEGORY === 'task' || firstItem.FCATEGORY === '任务') {
              console.log('[TEST] 任务记录的项目关联字段:');
              console.log(`  FOWNPROJECTID = ${firstItem.FOWNPROJECTID || '未定义'}`);
              
              // 查找其他可能的项目关联字段
              const projFields = Object.keys(firstItem).filter(k => 
                k.toLowerCase().includes('proj') || k.toLowerCase().includes('project')
              );
              
              if (projFields.length > 0) {
                console.log('[TEST] 其他可能与项目相关的字段:');
                projFields.forEach(field => {
                  console.log(`  ${field} = ${firstItem[field] || '未定义'}`);
                });
              }
            }
          }
        } else {
          console.log('[TEST] 响应格式不符合预期:', data);
        }
      })
      .catch(error => {
        console.error('[TEST] 获取项目和任务数据出错:', error);
      });
    },
    
    // 添加缓存清理方法
    clearCache() {
      console.log('[WorkReport] 清除缓存...');
      
      // 强制下次加载时不使用缓存（实际上这个标志已经不需要，因为我们默认强制刷新）
      this.cacheNeedsRefresh = true;
      
      // 获取用户信息，用于生成缓存键
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const username = userInfo.username || '';
      const reportDate = this.formatDate(this.selectedDate);
      
      // 清除工时报告API缓存（使用缓存工具函数）
      if (username && reportDate) {
        try {
          // 导入缓存工具函数并清除对应缓存
          import('../utils/cache').then(({ clearWorkReportCache }) => {
            clearWorkReportCache(reportDate, username);
            console.log(`[WorkReport] 已清除工时报告API缓存: ${reportDate}, ${username}`);
          }).catch(error => {
            console.warn('[WorkReport] 清除API缓存失败:', error);
          });
        } catch (error) {
          console.warn('[WorkReport] 导入缓存工具失败:', error);
        }
      }
      
      // 清除本地任务存储中当前日期的数据
      try {
        const savedTasks = localStorage.getItem('workReportTasks');
        if (savedTasks) {
          const parsedTasks = JSON.parse(savedTasks);
          // 过滤掉当前日期的任务，保留其他日期的任务
          const filteredTasks = parsedTasks.filter(task => task.date !== reportDate);
          localStorage.setItem('workReportTasks', JSON.stringify(filteredTasks));
          console.log(`[WorkReport] 已清除本地存储中${reportDate}的任务数据`);
        }
      } catch (error) {
        console.warn('[WorkReport] 清除本地任务存储失败:', error);
      }
      
      console.log('[WorkReport] 缓存已清除，下次数据加载将从服务器获取最新数据');
    },
    
    // 日历相关方法
    switchCalendarMode(mode) {
      this.calendarMode = mode;
    },
    // 在周和月模式之间切换
    toggleWeekMonthMode() {
      if (this.calendarMode === 'week') {
        this.calendarMode = 'month';
      } else {
        this.calendarMode = 'week';
      }
    },
    goToPreviousPeriod() {
      const date = new Date(this.currentDate);
      if (this.calendarMode === 'week') {
        date.setDate(date.getDate() - 7);
      } else if (this.calendarMode === 'month') {
        date.setMonth(date.getMonth() - 1);
      } else {
        date.setFullYear(date.getFullYear() - 1);
      }
      this.currentDate = date;
    },
    goToNextPeriod() {
      const date = new Date(this.currentDate);
      if (this.calendarMode === 'week') {
        date.setDate(date.getDate() + 7);
      } else if (this.calendarMode === 'month') {
        date.setMonth(date.getMonth() + 1);
      } else {
        date.setFullYear(date.getFullYear() + 1);
      }
      this.currentDate = date;
    },
    getWeekDays() {
      const days = [];
      const currentDay = this.currentDate.getDay(); // 0-6, 0是周日
      const currentDateObj = new Date(this.currentDate);
      
      // 转换为周一开始的排列，找到本周的周一
      const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;
      const firstDayOfWeek = new Date(currentDateObj);
      firstDayOfWeek.setDate(currentDateObj.getDate() + mondayOffset);
      
      // 生成一周的日期（周一到周日）
      for (let i = 0; i < 7; i++) {
        const date = new Date(firstDayOfWeek);
        date.setDate(firstDayOfWeek.getDate() + i);
        const dateStr = this.formatDate(date);
        
        days.push({
          date: dateStr,
          dayNumber: date.getDate(),
          isCurrentDay: this.isCurrentDay(date),
          hasTask: this.hasTaskForDate(dateStr),
          taskStatus: this.getDateTaskStatus(dateStr)
        });
      }
      
      return days;
    },
    getMonthDays() {
      const days = [];
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();
      
      // 获取当月1号是星期几 (0=周日, 1=周一, ..., 6=周六)
      const firstDayOfMonth = new Date(year, month, 1);
      const firstDayWeekday = firstDayOfMonth.getDay();
      
      // 转换为周一开始的排列 (周一=0, 周二=1, ..., 周日=6)
      const firstDayOffset = firstDayWeekday === 0 ? 6 : firstDayWeekday - 1;
      
      // 计算上个月的最后几天（只在第一周需要时添加）
      if (firstDayOffset > 0) {
        const prevMonth = month === 0 ? 11 : month - 1;
        const prevYear = month === 0 ? year - 1 : year;
        const lastDayOfPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
        
        // 添加上个月的最后几天（最多6天）
        for (let i = firstDayOffset - 1; i >= 0; i--) {
          const day = lastDayOfPrevMonth - i;
          const date = new Date(prevYear, prevMonth, day);
          const dateStr = this.formatDate(date);
          days.push({
            date: dateStr,
            dayNumber: day,
            isCurrentDay: this.isCurrentDay(date),
            isCurrentMonth: false,
            isOtherMonth: true,
            isEmpty: false,
            hasTask: this.hasTaskForDate(dateStr),
            taskStatus: this.getDateTaskStatus(dateStr)
          });
        }
      }
      
      // 计算当月有多少天
      const lastDayOfMonth = new Date(year, month + 1, 0);
      const daysInMonth = lastDayOfMonth.getDate();
      
      // 添加当月的日期
      for (let i = 1; i <= daysInMonth; i++) {
        const date = new Date(year, month, i);
        const dateStr = this.formatDate(date);
        days.push({
          date: dateStr,
          dayNumber: i,
          isCurrentDay: this.isCurrentDay(date),
          isCurrentMonth: true,
          isOtherMonth: false,
          isEmpty: false,
          hasTask: this.hasTaskForDate(dateStr),
          taskStatus: this.getDateTaskStatus(dateStr)
        });
      }
      
      // 计算当月最后一天是星期几
      const lastDayOfMonthDate = new Date(year, month, daysInMonth);
      const lastDayWeekday = lastDayOfMonthDate.getDay();
      
      // 转换为周一开始的排列 (周一=0, 周二=1, ..., 周日=6)
      const lastDayOffset = lastDayWeekday === 0 ? 6 : lastDayWeekday - 1;
      
      // 计算需要添加下个月的天数（完成最后一周）
      const nextDaysNeeded = 6 - lastDayOffset; // 6是周日的位置
      
      if (nextDaysNeeded > 0 && nextDaysNeeded < 7) {
        const nextMonth = month === 11 ? 0 : month + 1;
        const nextYear = month === 11 ? year + 1 : year;
        
        // 添加下个月的前几天（只添加完成最后一周所需的天数）
        for (let i = 1; i <= nextDaysNeeded; i++) {
          const date = new Date(nextYear, nextMonth, i);
          const dateStr = this.formatDate(date);
          days.push({
            date: dateStr,
            dayNumber: i,
            isCurrentDay: this.isCurrentDay(date),
            isCurrentMonth: false,
            isOtherMonth: true,
            isEmpty: false,
            hasTask: this.hasTaskForDate(dateStr),
            taskStatus: this.getDateTaskStatus(dateStr)
          });
        }
      }
      
      return days;
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    isCurrentDay(date) {
      const today = new Date();
      return date.getDate() === today.getDate() &&
        date.getMonth() === today.getMonth() &&
        date.getFullYear() === today.getFullYear();
    },
    isSelectedDay(dateStr) {
      return this.formatDate(this.selectedDate) === dateStr;
    },
    selectDate(dateStr) {
      const [year, month, day] = dateStr.split('-').map(Number);
      this.selectedDate = new Date(year, month - 1, day);
      
      console.log(`[DEBUG] 选中日期: ${dateStr}, 当前任务数量: ${this.tasks.length}`);
      if (this.tasks.length > 0) {
        console.log(`[DEBUG] 当前任务状态详情:`, this.tasks.map(task => ({
          id: task.id,
          status: task.status,
          statusText: this.formatTaskStatus(task.status),
          date: task.date
        })));
      }
      
      // 立即检查这个日期的状态
      const taskStatus = this.getDateTaskStatus(dateStr);
      console.log(`[DEBUG] 选中日期 ${dateStr} 的任务状态: ${taskStatus}`);
      
      // 选择日期后，清空当前任务列表，然后重新加载
      this.tasks = [];
      
      // 重置更新标志，确保可以加载新选择日期的数据
      if (this.isUpdatingData) {
        console.log('[WorkReport] 选择了新日期，重置更新标志');
        this.isUpdatingData = false;
      }
      
      // 加载选定日期的任务
      this.loadTasks();
    },
    
    // 任务相关方法
    loadTasks(forceRefresh = false) {
      // 如果正在更新数据，则跳过
      if (this.isUpdatingData) {
        console.log('[WorkReport] 跳过重复的数据更新请求');
        return;
      }
      
      // 获取当前选中日期格式化为 YYYY-MM-DD
      const reportDate = this.formatDate(this.selectedDate);
      
      console.log(`[WorkReport] 开始加载日期 ${reportDate} 的任务数据${forceRefresh ? ' (强制刷新)' : ''}`);
      
      // 记录缓存状态
      if (this.cacheNeedsRefresh || forceRefresh) {
        console.log('[WorkReport] 检测到需要刷新缓存，将绕过缓存加载最新数据');
      }
      
      // 显示加载状态
      this.$toast.loading({
        message: '加载中...',
        forbidClick: true
      });
      
      // 从本地存储获取用户账号（不再使用userId）
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const username = userInfo.username || '';
      
      console.log(`[WorkReport] 当前用户账号: ${username || '未设置'}`);
      
      if (!username) {
        this.$toast.clear();
        this.$toast.fail('用户信息不完整，请重新登录');
        console.warn('[WorkReport] 用户账号不存在，无法加载数据');
        return;
      }
      
      // 检查API是否可用（网络状态检测）
      console.log('[WorkReport] 检查API可用性...');
      
      // 检查是否在离线模式
      checkApiAvailability()
        .then(isAvailable => {
          console.log(`[WorkReport] API可用性检查结果: ${isAvailable ? '可用' : '不可用'}`);
          
          // 调用API获取工时报告数据
          const apiOptions = {
            retries: isAvailable ? 1 : 0, // 如果API不可用，不尝试重试
            timeout: isAvailable ? 30000 : 5000, // 如果API不可用，使用较短超时时间
            // 默认强制刷新，每次都获取最新数据（不使用缓存）
            forceRefresh: true
          };
          
          console.log('[WorkReport] 开始调用API获取数据, 参数:', JSON.stringify({
            reportDate,
            username,
            apiOptions
          }));
          
          getWorkReportData(reportDate, username, apiOptions) // 改为传递username
            .then(result => {
              this.$toast.clear();
              console.log('[WorkReport] API调用结果:', JSON.stringify({
                success: result.success,
                message: result.message,
                taskCount: result.data?.tasks?.length || 0
              }));
              
              if (result.success) {
                // 打印原始API返回的JSON数据
                console.log('[WorkReport] 原始API返回的JSON数据:', JSON.stringify(result.data, null, 2));
                
                // 更新任务列表并进行去重处理
                const originalTasks = result.data.tasks || [];
                this.tasks = this.deduplicateTasksByName(originalTasks);

                if (this.tasks.length !== originalTasks.length) {
                  console.log(`[WorkReport] 从API加载数据时去重: 原始${originalTasks.length}个 -> 去重后${this.tasks.length}个`);
                }
                
                // 重置缓存标志
                if (this.cacheNeedsRefresh) {
                  this.cacheNeedsRefresh = false;
                  console.log('[WorkReport] 数据已刷新，重置缓存标志');
                }
                
                // 检查任务中是否包含FBillno
                if (this.tasks && this.tasks.length > 0) {
                  const firstTask = this.tasks[0];
                  if (firstTask.fbillno) {
                    // 如果任务中包含fbillno字段，则存储到Vuex Store
                    this.$store.dispatch('saveFBillno', firstTask.fbillno);
                    console.log('[WorkReport] 从任务列表中获取并存储FBillno:', firstTask.fbillno);
                  }
                }
                
                // 验证任务状态
                this.validateTaskStatus();
                console.log('[WorkReport] 成功加载工时报告数据, 任务数量:', this.tasks.length);

                // 使用智能预加载已报工时数据（避免并发请求过多）
                this.smartPreloadReportedHours();

                // 打印任务ID列表
                if (this.tasks.length > 0) {
                  console.log('[WorkReport] 任务ID列表:', this.tasks.map(task => task.id).join(', '));
                  console.log('[WorkReport] 任务属性列表:', Object.keys(this.tasks[0]).join(', '));

                  // 添加详细的状态日志
                  console.log('[WorkReport] 任务状态详情:');
                  this.tasks.forEach(task => {
                    console.log(`任务ID: ${task.id}, 状态代码: '${task.status}', 状态文本: '${this.formatTaskStatus(task.status)}', 项目: ${task.project}, 任务: ${task.taskName || '(项目)'}`);
                  });
                }
                
                // 如果是从提交后调用的，记录日志
                if (this.isUpdatingData) {
                  console.log('[WorkReport] 数据更新成功完成（来自提交后的单次更新）');
                }
              } else {
                // 如果API返回失败但没有错误，可能是没有数据
                this.tasks = [];
                console.warn('[WorkReport] 无法加载工时报告数据:', result.message);
                
                // 尝试从本地存储加载
                try {
                  console.log('[WorkReport] 尝试从本地存储加载数据');
                  const savedTasks = localStorage.getItem('workReportTasks');
                  if (savedTasks) {
                    const parsedTasks = JSON.parse(savedTasks);
                    // 仅显示当前选中日期的任务
                    const filteredTasks = parsedTasks.filter(task => task.date === reportDate);
                    // 对加载的任务进行去重处理
                    this.tasks = this.deduplicateTasksByName(filteredTasks);
                    // 验证任务状态
                    this.validateTaskStatus();

                    if (this.tasks.length !== filteredTasks.length) {
                      console.log(`[WorkReport] 从本地存储加载并去重: 原始${filteredTasks.length}个 -> 去重后${this.tasks.length}个`);
                    } else {
                      console.log(`[WorkReport] 从本地存储加载到 ${this.tasks.length} 条任务数据`);
                    }
                  } else {
                    console.log('[WorkReport] 本地存储中没有任务数据');
                  }
                } catch (error) {
                  console.error('[WorkReport] 加载本地任务失败:', error);
                }
              }
            })
            .catch(error => {
              this.$toast.clear();
              this.$toast.fail(`加载失败: ${error.message}`);
              console.error('[WorkReport] 加载工时报告数据错误:', error.message, error.stack);
              
              // 出错时尝试从本地存储加载
              try {
                console.log('[WorkReport] API调用失败，尝试从本地存储加载数据');
                const savedTasks = localStorage.getItem('workReportTasks');
                if (savedTasks) {
                  const parsedTasks = JSON.parse(savedTasks);
                  // 仅显示当前选中日期的任务
                  const filteredTasks = parsedTasks.filter(task => task.date === reportDate);
                  // 对加载的任务进行去重处理
                  this.tasks = this.deduplicateTasksByName(filteredTasks);
                  // 验证任务状态
                  this.validateTaskStatus();

                  if (this.tasks.length !== filteredTasks.length) {
                    console.log(`[WorkReport] 从本地存储加载并去重: 原始${filteredTasks.length}个 -> 去重后${this.tasks.length}个`);
                  } else {
                    console.log(`[WorkReport] 从本地存储加载到 ${this.tasks.length} 条任务数据`);
                  }
                } else {
                  console.warn('[WorkReport] 本地存储中没有任务数据');
                }
              } catch (storageError) {
                console.error('[WorkReport] 加载本地任务失败:', storageError.message, storageError.stack);
              }
            });
        })
        .catch(() => {
          this.$toast.clear();
          console.warn('[WorkReport] 网络检查失败');
          this.$toast.fail('网络连接失败，请检查网络设置');
          
          // 尝试从本地存储加载
          try {
            console.log('[WorkReport] 尝试从本地存储加载数据');
            const savedTasks = localStorage.getItem('workReportTasks');
            if (savedTasks) {
              const parsedTasks = JSON.parse(savedTasks);
              // 仅显示当前选中日期的任务
              const filteredTasks = parsedTasks.filter(task => task.date === reportDate);
              // 对加载的任务进行去重处理
              this.tasks = this.deduplicateTasksByName(filteredTasks);
              // 验证任务状态
              this.validateTaskStatus();

              if (this.tasks.length !== filteredTasks.length) {
                console.log(`[WorkReport] 从本地存储加载并去重: 原始${filteredTasks.length}个 -> 去重后${this.tasks.length}个`);
              } else {
                console.log(`[WorkReport] 从本地存储加载到 ${this.tasks.length} 条任务数据`);
              }
            } else {
              console.log('[WorkReport] 本地存储中没有任务数据');
            }
          } catch (error) {
            console.error('[WorkReport] 加载本地任务失败:', error);
          }
        });
    },
    hasTaskForDate(dateStr) {
      // 模拟检查日期是否有任务，实际应该从API获取
      // 这里使用日期字符串进行判断，确保dateStr被使用
      return Math.random() > 0.7 && dateStr !== '';
    },
    /**
     * 判断指定日期是否为周六、周日或法定节假日
     * @param {string} dateStr 日期字符串，格式：YYYY-MM-DD
     * @returns {boolean} true-是周末或节假日，false-是工作日
     */
    isWeekendOrHoliday(dateStr) {
      try {
        // 创建日期对象
        const date = new Date(dateStr + 'T00:00:00');
        const dayOfWeek = date.getDay(); // 0-6, 0是周日, 1是周一, ..., 6是周六
        
        // 判断是否为周末（周六或周日）
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        
        // 判断是否为法定节假日
        const isHoliday = this.holidayList.includes(dateStr);
        
        const result = isWeekend || isHoliday;
        console.log(`[WorkReport] 日期 ${dateStr} 周末/节假日判断: 星期${dayOfWeek}, 是否周末: ${isWeekend}, 是否节假日: ${isHoliday}, 最终结果: ${result}`);
        
        return result;
      } catch (error) {
        console.error(`[WorkReport] 判断日期 ${dateStr} 是否为周末/节假日时出错:`, error);
        return false; // 出错时默认为工作日
      }
    },
    
    getDateTaskStatus(dateStr) {
      // 检查指定日期的任务审核状态
      // 返回值：'approved' - 工时满8小时显示对勾, 'none' - 工时不足8小时或无数据显示红点, null - 今天以后不显示任何指示器
      
      console.log(`[WorkReport] 检查日期 ${dateStr} 的工时状态`);
      
      // 获取今天的日期字符串 (格式: YYYY-MM-DD)
      const today = new Date();
      const todayStr = today.getFullYear() + '-' + 
                      String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                      String(today.getDate()).padStart(2, '0');
      
      // 如果是今天以后的日期，不显示任何指示器
      if (dateStr > todayStr) {
        console.log(`[WorkReport] ${dateStr} 是未来日期，不显示任何指示器`);
        return null;
      }
      
      // 如果有dateStatusMap缓存数据，直接使用（包括周末数据）
      if (this.dateStatusMap && this.dateStatusMap[dateStr] !== undefined) {
        const status = this.dateStatusMap[dateStr];
        console.log(`[WorkReport] 从缓存获取 ${dateStr} 状态: ${status}`);
        return status;
      }
      
      // 如果没有缓存数据，检查是否为周末或节假日
      if (this.isWeekendOrHoliday(dateStr)) {
        console.log(`[WorkReport] ${dateStr} 是周末/节假日且无缓存数据，不显示指示器（周末不要求工作）`);
        return null;
      } else {
        console.log(`[WorkReport] ${dateStr} 是工作日且无缓存数据，显示红点`);
        return 'none';
      }
    },
    showAddTaskPopup() {
      console.log('[WorkReport] 显示添加任务 - 直接打开项目/任务选择器');
      
      // 设置为添加任务模式
      this.isAddingNewTask = true;
      this.isEditingTask = false;
      
      // 清空选择状态
      this.taskForm = {
        taskName: '',
        taskCode: '',
        taskContent: '',
        project: '',
        projectName: '',
        task: '',
        regularHours: 0,
        overtimeHours: 0,
        progress: 0,
        date: this.formatDate(new Date()),
        isProjectLevel: false,
        plannedHours: 0,
        startDate: '',
        endDate: '',
        plannedStartDate: '',
        plannedEndDate: '',
        position: '',
        positionName: '',
        allSearchResults: []
      };
      
      // 清空搜索和过滤状态
      this.searchKeyword = '';
      this.filterType = 'all';
      this.searchType = 'all';
      
      // 默认开启多选模式
      this.isMultiSelectMode = true;
      this.selectedItems = []; // 清空选中项
      
      // 直接显示项目/任务选择器
      this.showCombinedPicker = true;
      
      console.log('[WorkReport] 添加任务模式已启动，等待用户选择项目/任务，默认多选模式');
    },
    closeTaskForm() {
      this.showTaskForm = false;
      // 重置添加和编辑模式标志
      this.isAddingNewTask = false;
      this.isEditingTask = false;
      console.log('[WorkReport] 任务表单已关闭，所有模式标志已重置');
    },
    editTask(task) {
      console.log(`[WorkReport] 开始编辑任务: ID=${task.id}, 项目="${task.projectName || task.project}", 任务="${task.taskName}"`);

      // 检查任务是否可编辑
      if (!this.canEditTask(task)) {
        console.log(`[WorkReport] 任务 ${task.id} 不可编辑，状态: ${task.status}`);
        this.$toast.fail('已提交或已审核的任务不可编辑');
        return;
      }

      // 不再区分显示模式，直接启动工作内容字段的内联编辑
      console.log(`[WorkReport] 启动任务 ${task.id} 工作内容的内联编辑`);
      this.startEditing(task.id, 'taskContent');
    },
    deleteTask(task) {
      console.log(`[WorkReport] 准备删除任务: ID=${task.id}, 项目="${task.projectName || task.project}", 任务="${task.taskName}", 状态="${task.status}", fbillno="${task.fbillno || '无'}"`);
      
      // 检查任务状态，只允许删除状态为'a'(暂存)或'd'(驳回)的任务
      if (!this.canDeleteTask(task)) {
        const statusText = this.formatTaskStatus(task.status);
        console.log(`[WorkReport] 删除被拒绝: 任务状态为"${statusText}"，不允许删除`);
        this.$toast.fail(`无法删除"${statusText}"状态的任务，只能删除"暂存"或"驳回"状态的任务`);
        return;
      }
      
      // 判断是虚拟任务还是已保存任务
      const isVirtualTask = !task.fbillno && !task.billNumber;
      const deleteType = isVirtualTask ? '虚拟任务' : '已保存任务';
      
      console.log(`[WorkReport] 识别删除类型: ${deleteType}`);
      
      this.$dialog.confirm({
        title: '确认删除',
        message: `确定要删除"${task.projectName || task.project} ${task.taskName ? '- ' + task.taskName : ''}"吗？${isVirtualTask ? '（未保存的任务）' : ''}`,
      })
        .then(() => {
          // 用户确认删除
          console.log('[WorkReport] 用户确认删除');
          
          if (isVirtualTask) {
            // 虚拟任务：直接从本地数组中移除
            console.log(`[WorkReport] 执行虚拟任务删除，无需调用API`);
            
            const index = this.tasks.findIndex(t => t.id === task.id);
            if (index !== -1) {
              this.tasks.splice(index, 1);
              console.log(`[WorkReport] 已从本地数组删除虚拟任务，当前任务数量: ${this.tasks.length}`);
              
              // 自动保存到本地存储
              this.saveTasks();
              
              // 显示成功提示
              this.$toast.success('删除成功');
            } else {
              console.warn(`[WorkReport] 未在本地数组中找到要删除的虚拟任务: ${task.id}`);
              this.$toast.fail('删除失败：任务未找到');
            }
          } else {
            // 已保存任务：调用API删除
            console.log(`[WorkReport] 执行已保存任务删除，调用API`);
            
            // 显示删除加载状态
            this.$toast.loading({
              message: '正在删除...',
              forbidClick: true,
            });
            
            const billNumber = task.fbillno || task.billNumber || task.id;
            console.log(`[WorkReport] 准备调用删除API，单据号: ${billNumber}`);
            
            // 调用删除API
            deleteWorkReportData(billNumber)
              .then(deleteResult => {
                this.$toast.clear();
                console.log('[WorkReport] 删除API调用结果:', deleteResult);
                
                if (deleteResult.success) {
                  // API删除成功，从本地数组中移除
                  const index = this.tasks.findIndex(t => t.id === task.id);
                  if (index !== -1) {
                    this.tasks.splice(index, 1);
                    console.log(`[WorkReport] 已从本地数组删除任务，当前任务数量: ${this.tasks.length}`);
                    
                    // 自动保存到本地存储
                    this.saveTasks();
                    
                    // 清除缓存
                    this.clearCache();
                    
                    // 显示成功提示
                    this.$toast.success('删除成功');
                    
                    // 强制刷新任务列表以确保数据同步，绕过所有缓存
                    console.log('[WorkReport] 删除成功后强制刷新任务列表，绕过缓存');
                    
                    // 立即清除缓存并标记需要强制刷新
                    this.cacheNeedsRefresh = true;
                    
                    // 延迟刷新，确保服务器端数据已更新，并强制从服务器获取
                    setTimeout(() => {
                      this.loadTasks(true); // 传递true参数表示强制刷新
                    }, 500);
                  } else {
                    console.warn(`[WorkReport] 未在本地数组中找到要删除的任务: ${task.id}`);
                    this.$toast.success('删除成功（服务器端已删除）');
                  }
                } else {
                  // API删除失败
                  console.error('[WorkReport] 删除API失败:', deleteResult.message);
                  this.$toast.fail(`删除失败: ${deleteResult.message}`);
                }
              })
              .catch(error => {
                this.$toast.clear();
                console.error('[WorkReport] 删除API调用异常:', error);
                this.$toast.fail(`删除失败: ${error.message}`);
              });
          }
        })
        .catch(() => {
          // 用户取消删除
          console.log('[WorkReport] 用户取消删除');
        });
    },
    saveTask() {
      console.log('[WorkReport] 开始保存任务表单');
      
      // 表单验证
      if (!this.taskForm.project) {
        console.log('[WorkReport] 表单验证失败: 未选择项目');
        this.$toast('请选择项目');
        return;
      }
      // 如果不是项目级别的工时汇报，则需要填写任务名称
      if (!this.taskForm.isProjectLevel && !this.taskForm.taskCode) {
        console.log('[WorkReport] 表单验证失败: 未选择任务且未勾选项目工作');
        this.$toast('请选择任务或勾选"项目工作"');
        return;
      }
      if (!this.taskForm.taskContent.trim()) {
        console.log('[WorkReport] 表单验证失败: 未填写工作内容');
        this.$toast('请输入工作内容');
        return;
      }
      
      console.log('[WorkReport] 表单验证通过');
      
      // 打印搜索结果数量信息
      if (this.taskForm.allSearchResults && this.taskForm.allSearchResults.length > 0) {
        console.log(`[WorkReport] 保存的搜索结果数量: ${this.taskForm.allSearchResults.length}`);
        // 可以在这里处理搜索结果数据，例如提取统计信息等
      }
      
      // 确保岗位信息正确设置
      if (this.taskForm.positionName && !this.taskForm.position) {
        // 如果有岗位名称但没有岗位编号，尝试从岗位选项中查找
        const matchingPosition = this.positionOptions.find(pos => pos.text === this.taskForm.positionName);
        if (matchingPosition) {
          console.log(`[WorkReport] 为任务表单补充岗位编号: ${matchingPosition.value}`);
          this.taskForm.position = matchingPosition.value;
        } else {
          // 如果找不到匹配的岗位编号，显示警告
          console.warn(`[WorkReport] 警告: 未找到岗位名称"${this.taskForm.positionName}"对应的编号`);
          this.$toast('未找到对应的岗位编号，请重新选择岗位');
          return; // 阻止保存
        }
      } else if (this.taskForm.position && !this.taskForm.positionName) {
        // 如果有岗位编号但没有岗位名称，尝试从岗位选项中查找
        const matchingPosition = this.positionOptions.find(pos => pos.value === this.taskForm.position);
        if (matchingPosition) {
          console.log(`[WorkReport] 为任务表单补充岗位名称: ${matchingPosition.text}`);
          this.taskForm.positionName = matchingPosition.text;
        }
      } else if (!this.taskForm.position) {
        // 如果没有设置岗位信息，显示警告
        console.warn('[WorkReport] 警告: 未设置岗位信息');
        this.$toast('请选择岗位');
        return; // 阻止保存
      }
      
      // 记录最终的岗位信息
      console.log(`[WorkReport] 保存前的岗位信息: 编码=${this.taskForm.position || '未设置'}, 名称=${this.taskForm.positionName || '未设置'}`);
      
      // 确保数值字段为数字类型
      const formData = {
        ...this.taskForm,
        regularHours: parseFloat(this.taskForm.regularHours) || 0,
        overtimeHours: parseFloat(this.taskForm.overtimeHours) || 0,
        progress: parseFloat(this.taskForm.progress) || 0,
        plannedHours: parseFloat(this.taskForm.plannedHours) || 0,
        position: this.taskForm.position,             // 岗位编号
        positionName: this.taskForm.positionName,     // 岗位名称
        plannedStartDate: this.taskForm.plannedStartDate || '',
        plannedEndDate: this.taskForm.plannedEndDate || '',
        // 保留搜索结果
        allSearchResults: this.taskForm.allSearchResults || []
      };
      
      // 确保保留FOWNPROJECT字段（如果存在）
      if (this.taskForm.ownProject && !formData.ownProject) {
        formData.ownProject = this.taskForm.ownProject;
        console.log(`[WorkReport] 保留任务的FOWNPROJECT字段值: "${formData.ownProject}"`);
      }
      
      // 针对敏捷任务的FOWNPROJECT处理逻辑
      if (this.taskForm.taskType === 'agile' && this.taskForm.project) {
        // 对于敏捷任务，将项目编码设置为FOWNPROJECT
        formData.ownProject = this.taskForm.project;
        console.log(`[WorkReport] 敏捷任务设置FOWNPROJECT为项目编码: "${formData.ownProject}"`);
      }
      
      // 打印岗位信息
      console.log(`[WorkReport] 保存任务的岗位信息: 编号=${formData.position || '未设置'}, 名称=${formData.positionName || '未设置'}`);
      
      if (this.isEditingTask) {
        // 编辑现有任务
        const index = this.tasks.findIndex(t => t.id === this.currentTaskId);
        if (index !== -1) {
          this.tasks[index] = { ...this.tasks[index], ...formData, id: this.currentTaskId };
          console.log(`[WorkReport] 更新任务: ID=${this.currentTaskId}, 项目="${formData.projectName}", 任务="${formData.taskName}"`);
        }
      } else {
        // 添加新任务
        const taskId = `task-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const newTask = {
          id: taskId,
          ...formData,
          date: this.formatDate(this.selectedDate),
          status: 'a' // 设置默认状态为"暂存"(状态代码'a')
        };
        console.log(`[WorkReport] 添加新任务: ID=${taskId}, 项目="${newTask.projectName}", 任务="${newTask.taskName}"`);
        this.tasks.push(newTask);
        console.log(`[WorkReport] 当前任务数量: ${this.tasks.length}`);
      }
      
      // 保存到本地存储或发送到服务器
      this.saveTasks();
      
      // 清除缓存
      this.clearCache();
      
      this.$toast.success(this.isEditingTask ? '任务已更新' : '任务已添加');
      this.showTaskForm = false;
    },
    saveAllTasks() {
      console.log(`[WorkReport] 开始保存所有任务, 任务数量: ${this.tasks.length}`);
      if (this.tasks.length === 0) {
        console.log('[WorkReport] 无任务可保存');
        this.$toast.fail('无任务可保存');
        return;
      }

      // 过滤出状态为"暂存"的任务
      const savableTasks = this.tasks.filter(task => {
        // 将状态码转为小写进行比较
        const status = (task.status || '').toLowerCase();
        return status === 'a' || status === 'd'; // 保存状态为"暂存"或"重新审核"的任务
      });
      
      console.log(`[WorkReport] 可保存的任务数量: ${savableTasks.length}/${this.tasks.length}`);
      
      if (savableTasks.length === 0) {
        console.log('[WorkReport] 没有可保存的任务，所有任务已提交或已审核');
        this.$toast.fail('没有可保存的任务');
        return;
      }

      // 显示任务详情
      console.log('[WorkReport] 保存的任务详情:');
      let tasksWithOwnProject = 0;
      savableTasks.forEach((task, index) => {
        if (task.ownProject) {
          tasksWithOwnProject++;
        }
        console.log(`[WorkReport] 任务 ${index + 1}/${savableTasks.length}:`, 
          `项目: ${task.project || '无'} (${task.projectName || '无名称'}), ` +
          `任务: ${task.taskName || '无'} (${task.taskCode || '无代码'}), ` +
          `所属项目: ${task.ownProject || '无'}, ` +
          `常规时间: ${task.regularHours || 0}, ` +
          `加班时间: ${task.overtimeHours || 0}, ` +
          `岗位: ${task.position || '无'} (${task.positionName || '无名称'}), ` +
          `状态: ${task.status || '无'}`
        );
      });
      
      console.log(`[WorkReport] 总共保存 ${savableTasks.length} 个任务，其中 ${tasksWithOwnProject} 个带有FOWNPROJECT字段`);
      
      // 首先保存到本地存储
      this.saveTasks();
      
      // 然后调用API
      this.$toast.loading({
        message: `正在保存${savableTasks.length}个暂存状态的任务...`,
        forbidClick: true,
      });
      
      // 确保每个任务都有position和positionName字段
      const tasksToSave = savableTasks.map(task => {
        // 如果任务有positionName但没有position，尝试从positionOptions中找到对应的position
        if (task.positionName && !task.position) {
          const matchingPosition = this.positionOptions.find(pos => pos.text === task.positionName);
          if (matchingPosition) {
            console.log(`[WorkReport] 为任务补充岗位编号: ${matchingPosition.value}`);
            return {
              ...task,
              position: matchingPosition.value
            };
          } else {
            console.warn(`[WorkReport] 警告: 未找到岗位名称"${task.positionName}"对应的编号`);
            // 使用默认岗位编号或空值
            return {
              ...task,
              position: 'default_position_code' // 或者使用一个默认值
            };
          }
        }
        // 如果任务有position但没有positionName，尝试从positionOptions中找到对应的positionName
        else if (task.position && !task.positionName) {
          const matchingPosition = this.positionOptions.find(pos => pos.value === task.position);
          if (matchingPosition) {
            console.log(`[WorkReport] 为任务补充岗位名称: ${matchingPosition.text}`);
            return {
              ...task,
              positionName: matchingPosition.text
            };
          }
        }
        // 如果任务没有任何岗位信息，添加默认岗位
        else if (!task.position && !task.positionName) {
          console.warn(`[WorkReport] 警告: 任务ID=${task.id}没有岗位信息，使用默认岗位`);
          // 使用默认岗位
          return {
            ...task,
            position: 'default_position_code', // 默认岗位编码
            positionName: '默认岗位' // 默认岗位名称
          };
        }
        return task;
      });
      
      // 添加日志记录保存前的任务岗位信息
      console.log('[WorkReport] 保存前的任务岗位信息:');
      tasksToSave.forEach((task, index) => {
        console.log(`任务${index + 1}: 岗位编码=${task.position || '未设置'}, 岗位名称=${task.positionName || '未设置'}`);
      });
      
      // 在调用API前检查所有任务是否都有岗位编码
      const missingPositionTasks = tasksToSave.filter(task => !task.position);
      if (missingPositionTasks.length > 0) {
        console.error(`[WorkReport] 错误: ${missingPositionTasks.length}个任务缺少岗位编码`);
        this.$toast.fail(`有${missingPositionTasks.length}个任务缺少岗位编码，请检查`);
        return;
      }
      
      // 记录保存结果
      let successCount = 0;
      let failedTasks = [];
      
      // 串行为每个任务调用保存API（避免并发冲突）
      const saveTasksSequentially = async () => {
        for (let index = 0; index < tasksToSave.length; index++) {
          const task = tasksToSave[index];
          
          // 更新进度提示
          this.$toast.loading({
            message: `正在保存第${index + 1}/${tasksToSave.length}个任务...`,
            forbidClick: true
          });
          
          console.log(`[WorkReport] 保存第${index + 1}个任务:`, task.projectName || task.project, task.taskName || '(项目级工作)');
          
          try {
            // 调用API保存单个任务（串行调用，等待前一个完成）
            const response = await saveWorkReportData([task]);
            console.log(`[WorkReport] 第${index + 1}个任务保存响应:`, response);
          
          if (response.success) {
              successCount++;
              console.log(`[WorkReport] 第${index + 1}个任务保存成功`);
            
              // 为每个成功保存的任务处理FBillno
              this.handleFBillno(response, task.id);
              
              // 检查是否为耗费任务，如果是则直接设置为已审核状态
              if (task.taskType === 'timecost') {
                const taskIndex = this.tasks.findIndex(t => t.id === task.id);
                if (taskIndex !== -1) {
                  this.tasks[taskIndex].status = 'c'; // 设置为已审核状态
                  this.tasks[taskIndex].statusDisplay = '已审核';
                  console.log(`[WorkReport] 耗费任务 ${task.taskName} 保存后自动设置为已审核状态`);
                }
              }
            
              // 只为第一个成功的任务清除缓存（避免重复清除）
              if (index === 0) {
                this.clearCache();
              }
            } else {
              console.error(`[WorkReport] 第${index + 1}个任务保存失败:`, response.message);
              failedTasks.push({ task, error: response.message });
            }
          } catch (error) {
            console.error(`[WorkReport] 第${index + 1}个任务保存异常:`, error);
            failedTasks.push({ task, error: error.message || '网络错误' });
          }
          
          // 如果不是最后一个任务，添加小延迟避免请求过于频繁
          if (index < tasksToSave.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500)); // 500ms延迟
          }
        }
      };
      
      // 执行串行保存
      saveTasksSequentially()
        .then(() => {
          this.$toast.clear();
          
          console.log('[WorkReport] 批量保存完成，结果统计:');
          console.log(`- 成功: ${successCount}/${tasksToSave.length}`);
          console.log(`- 失败: ${failedTasks.length}/${tasksToSave.length}`);
          
          if (failedTasks.length > 0) {
            console.log('失败的任务:', failedTasks);
          }
          
          // 保存更新后的任务状态到本地存储（包含临时任务和耗费任务的状态变更）
          this.saveTasks();
          
          // 根据保存结果提供不同的用户反馈
          if (successCount === tasksToSave.length) {
            // 全部成功
            this.$toast.success({
              message: `已成功保存${successCount}个暂存状态的任务`,
              duration: 2000
            });
          } else if (successCount > 0) {
            // 部分成功
            this.$toast.fail({
              message: `共${tasksToSave.length}个任务，其中${successCount}个保存成功，${failedTasks.length}个保存失败`,
              duration: 3000
            });
          } else {
            // 全部失败
            this.$toast.fail({
              message: `保存失败，共${tasksToSave.length}个任务均未能保存`,
              duration: 3000
            });
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('[WorkReport] 串行保存过程中发生错误:', error);
          this.$toast.fail({
            message: `保存过程中发生错误: ${error.message || '未知错误'}`,
            duration: 3000
          });
        });
    },
    submitTasks() {
      console.log(`[WorkReport] 准备提交任务, 任务数量: ${this.tasks.length}`);
      if (this.tasks.length === 0) {
        console.log('[WorkReport] 无任务可提交');
        this.$toast.fail('无任务可提交');
        return;
      }
      
      // 过滤出状态为"暂存"或"重新审核"的任务
      const submittableTasks = this.tasks.filter(task => {
        // 将状态码转为小写进行比较
        const status = (task.status || '').toLowerCase();
        return status === 'a' || status === 'd'; // 提交状态为"暂存"或"重新审核"的任务
      });
      
      console.log(`[WorkReport] 可提交的任务数量: ${submittableTasks.length}/${this.tasks.length}`);
      
      if (submittableTasks.length === 0) {
        console.log('[WorkReport] 没有可提交的任务，所有任务已提交或已审核');
        this.$toast.fail('没有可提交的任务');
        return;
      }
      
      // 检查是否有任务没有fbillno（需要先保存）
      const tasksWithoutFbillno = submittableTasks.filter(task => !task.fbillno);
      
      // 新增逻辑：如果有未保存的任务，自动调用保存功能
      if (tasksWithoutFbillno.length > 0) {
        console.log(`[WorkReport] 发现${tasksWithoutFbillno.length}个任务没有单据号，自动调用保存功能`);
        
        this.$toast.loading({
          message: `正在自动保存${tasksWithoutFbillno.length}个未保存的任务...`,
          forbidClick: true
        });
        
        // 调用保存逻辑
        this.autoSaveBeforeSubmit(submittableTasks);
        return;
      }
      
      // 如果所有任务都已保存，直接进入提交流程
      this.proceedWithSubmission(submittableTasks);
    },
    autoSaveBeforeSubmit(submittableTasks) {
      // 自动保存未保存的任务
      const tasksToSave = submittableTasks.filter(task => !task.fbillno);
      
      // 记录保存结果
      let successCount = 0;
      let failedTasks = [];
      
      // 串行为每个任务调用保存API（避免并发冲突）
      const saveTasksSequentially = async () => {
        for (let index = 0; index < tasksToSave.length; index++) {
          const task = tasksToSave[index];
          
          // 更新进度提示
          this.$toast.loading({
            message: `正在保存第${index + 1}/${tasksToSave.length}个任务...`,
            forbidClick: true
          });
          
          console.log(`[WorkReport] 保存第${index + 1}个任务:`, task.projectName || task.project, task.taskName || '(项目级工作)');
          
          try {
            // 调用API保存单个任务（串行调用，等待前一个完成）
            const response = await saveWorkReportData([task]);
            console.log(`[WorkReport] 第${index + 1}个任务保存响应:`, response);
          
          if (response.success) {
              successCount++;
              console.log(`[WorkReport] 第${index + 1}个任务保存成功`);
            
              // 为每个成功保存的任务处理FBillno
              this.handleFBillno(response, task.id);
              
              // 检查是否为耗费任务，如果是则直接设置为已审核状态
              if (task.taskType === 'timecost') {
                const taskIndex = this.tasks.findIndex(t => t.id === task.id);
                if (taskIndex !== -1) {
                  this.tasks[taskIndex].status = 'c'; // 设置为已审核状态
                  this.tasks[taskIndex].statusDisplay = '已审核';
                  console.log(`[WorkReport] 耗费任务 ${task.taskName} 保存后自动设置为已审核状态`);
                }
              }
            
              // 只为第一个成功的任务清除缓存（避免重复清除）
              if (index === 0) {
                this.clearCache();
              }
            } else {
              console.error(`[WorkReport] 第${index + 1}个任务保存失败:`, response.message);
              failedTasks.push({ task, error: response.message });
            }
          } catch (error) {
            console.error(`[WorkReport] 第${index + 1}个任务保存异常:`, error);
            failedTasks.push({ task, error: error.message || '网络错误' });
          }
          
          // 如果不是最后一个任务，添加小延迟避免请求过于频繁
          if (index < tasksToSave.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500)); // 500ms延迟
          }
        }
      };
      
      // 执行串行保存
      saveTasksSequentially()
        .then(() => {
          this.$toast.clear();
          
          console.log('[WorkReport] 批量保存完成，结果统计:');
          console.log(`- 成功: ${successCount}/${tasksToSave.length}`);
          console.log(`- 失败: ${failedTasks.length}/${tasksToSave.length}`);
          
          if (failedTasks.length > 0) {
            console.log('失败的任务:', failedTasks);
          }
          
          // 保存更新后的任务状态到本地存储（包含临时任务和耗费任务的状态变更）
          this.saveTasks();
          
          // 根据保存结果提供不同的用户反馈
          if (successCount === tasksToSave.length) {
            // 全部成功
            this.$toast.success({
              message: `已成功保存${successCount}个未保存的任务`,
              duration: 2000
            });
            
            // 重新筛选可提交的任务，确保使用最新的任务数据（包含fbillno）
            const updatedSubmittableTasks = this.tasks.filter(task => {
              const status = (task.status || '').toLowerCase();
              return status === 'a' || status === 'd'; // 提交状态为"暂存"或"重新审核"的任务
            });
            
            console.log('[WorkReport] 保存完成后重新筛选可提交任务:');
            console.log(`- 原始任务数: ${submittableTasks.length}`);
            console.log(`- 更新后任务数: ${updatedSubmittableTasks.length}`);
            console.log('- 更新后任务fbillno状态:', updatedSubmittableTasks.map(task => ({
              id: task.id,
              projectName: task.projectName || task.project,
              taskName: task.taskName,
              fbillno: task.fbillno || '无'
            })));
            
            // 使用更新后的任务数据进入提交流程
            this.proceedWithSubmission(updatedSubmittableTasks);
          } else if (successCount > 0) {
            // 部分成功
            this.$toast.fail({
              message: `共${tasksToSave.length}个任务，其中${successCount}个保存成功，${failedTasks.length}个保存失败`,
              duration: 3000
            });
          } else {
            // 全部失败
            this.$toast.fail({
              message: `保存失败，共${tasksToSave.length}个任务均未能保存`,
              duration: 3000
            });
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('[WorkReport] 串行保存过程中发生错误:', error);
          this.$toast.fail({
            message: `保存过程中发生错误: ${error.message || '未知错误'}`,
            duration: 3000
          });
        });
    },
    proceedWithSubmission(submittableTasks) {
      // 显示任务详情
      console.log('[WorkReport] 提交的任务详情:');
      let totalRegularHours = 0;
      let totalOvertimeHours = 0;
      let tasksWithOwnProject = 0;
      
      submittableTasks.forEach((task, index) => {
        totalRegularHours += parseFloat(task.regularHours || 0);
        totalOvertimeHours += parseFloat(task.overtimeHours || 0);
        
        if (task.ownProject) {
          tasksWithOwnProject++;
        }
        
        console.log(`[WorkReport] 任务 ${index + 1}/${submittableTasks.length}:`, 
          `项目: ${task.project || '无'} (${task.projectName || '无名称'}), ` +
          `任务: ${task.taskName || '无'} (${task.taskCode || '无代码'}), ` +
          `所属项目: ${task.ownProject || '无'}, ` +
          `常规时间: ${task.regularHours || 0}, ` +
          `加班时间: ${task.overtimeHours || 0}, ` +
          `岗位: ${task.position || '无'} (${task.positionName || '无名称'}), ` +
          `状态: ${task.status || '无'}, ` +
          `单据号: ${task.fbillno || '无'}`
        );
      });
      
      console.log(`[WorkReport] 总计: 常规时间=${totalRegularHours.toFixed(2)}小时, 加班时间=${totalOvertimeHours.toFixed(2)}小时, 带FOWNPROJECT任务=${tasksWithOwnProject}个`);
      
      // 首先保存到本地存储
      this.saveTasks();
      
      // 提示信息，区分部分提交和全部提交的情况
      let confirmMessage = '';
      if (submittableTasks.length === this.tasks.length) {
        confirmMessage = `确定要提交这${submittableTasks.length}个任务吗？提交后将无法修改。`;
      } else {
        confirmMessage = `共有${this.tasks.length}个任务，其中${submittableTasks.length}个可提交（状态为"暂存"）。确定要提交这些任务吗？提交后将无法修改。`;
      }
      
      // 显示确认对话框
      this.$dialog.confirm({
        title: '提交确认',
        message: confirmMessage,
      })
        .then(() => {
          // 用户点击确认
          console.log('[WorkReport] 用户确认提交');
          
          // 创建任务状态的副本，以便可以更新部分任务的状态
          const updatedTasks = [...this.tasks];
          
          // 只更新要提交的任务的状态
          submittableTasks.forEach(submittableTask => {
            const taskIndex = updatedTasks.findIndex(t => t.id === submittableTask.id);
            if (taskIndex !== -1) {
              updatedTasks[taskIndex] = {
                ...updatedTasks[taskIndex],
                status: 'b' // 设置状态为"已提交"(状态代码'b')
              };
            }
          });
      
          // 更新任务列表
          this.tasks = updatedTasks;
          
          // 打印要提交的任务数据
          console.log('[WorkReport] 提交任务 - 要提交的任务数据:', JSON.stringify(submittableTasks, null, 2));
          
          // 保存到本地存储
          this.saveTasks();
          
          this.$toast.loading({
            message: '正在提交...',
            forbidClick: true,
          });
          
          // 确保每个任务都有position和positionName字段
          const tasksToSubmit = submittableTasks.map(task => {
            // 如果任务有positionName但没有position，尝试从positionOptions中找到对应的position
            if (task.positionName && !task.position) {
              const matchingPosition = this.positionOptions.find(pos => pos.text === task.positionName);
              if (matchingPosition) {
                console.log(`[WorkReport] 为提交任务补充岗位编号: ${matchingPosition.value}`);
                return {
                  ...task,
                  position: matchingPosition.value
                };
              } else {
                console.warn(`[WorkReport] 警告: 未找到岗位名称"${task.positionName}"对应的编号`);
                // 使用默认岗位编号或空值
                return {
                  ...task,
                  position: 'default_position_code' // 或者使用一个默认值
                };
              }
            }
            // 如果任务有position但没有positionName，尝试从positionOptions中找到对应的positionName
            else if (task.position && !task.positionName) {
              const matchingPosition = this.positionOptions.find(pos => pos.value === task.position);
              if (matchingPosition) {
                console.log(`[WorkReport] 为提交任务补充岗位名称: ${matchingPosition.text}`);
                return {
                  ...task,
                  positionName: matchingPosition.text
                };
              }
            }
            // 如果任务没有任何岗位信息，添加默认岗位
            else if (!task.position && !task.positionName) {
              console.warn(`[WorkReport] 警告: 任务ID=${task.id}没有岗位信息，使用默认岗位`);
              // 使用默认岗位
              return {
                ...task,
                position: 'default_position_code', // 默认岗位编码
                positionName: '默认岗位' // 默认岗位名称
              };
            }
              return task;
          });
          
          // 添加日志记录提交前的任务岗位信息
          console.log('[WorkReport] 提交前的任务岗位信息:');
          tasksToSubmit.forEach((task, index) => {
            console.log(`任务${index + 1}: 岗位编码=${task.position || '未设置'}, 岗位名称=${task.positionName || '未设置'}`);
          });
          
          // 在调用API前检查所有任务是否都有岗位编码
          const missingPositionTasks = tasksToSubmit.filter(task => !task.position);
          if (missingPositionTasks.length > 0) {
            console.error(`[WorkReport] 错误: ${missingPositionTasks.length}个任务缺少岗位编码`);
            this.$toast.fail(`有${missingPositionTasks.length}个任务缺少岗位编码，请检查`);
            return;
          }
          
          // 调用API提交任务
          submitWorkReportData(tasksToSubmit)
            .then(response => {
              this.$toast.clear();
              console.log(`[WorkReport] 提交任务API响应:`, response);
              
              if (response.success) {
                // 打印API响应的完整数据
                console.log('[WorkReport] 提交任务 - API响应的完整数据:', JSON.stringify(response.data, null, 2));
                
                // 提取并存储FBillno
                this.handleFBillno(response);
                
                // 清除缓存
                this.clearCache();
                
                this.$toast.success({
                  message: '提交成功',
                  duration: 2000
                });
                
                // 标记数据更新状态，防止重复更新
                this.isUpdatingData = true;
                console.log('[WorkReport] 设置更新标志，防止重复更新数据');
                
                // 刷新任务列表（确保只调用一次）
                console.log('[WorkReport] 提交成功后更新任务列表 - 单次更新');
                this.loadTasks();
                
                // 设置一个短暂的延时，然后重置更新标志
                setTimeout(() => {
                  this.isUpdatingData = false;
                  console.log('[WorkReport] 数据更新完成，重置更新标志');
                }, 1000);
              } else {
                this.$toast.fail({
                  message: `提交失败: ${response.message || '未知错误'}`,
                  duration: 3000
                });
              }
            })
            .catch(error => {
              this.$toast.clear();
              console.error(`[WorkReport] 提交任务失败:`, error);
              this.$toast.fail({
                message: `提交失败: ${error.message || '网络错误'}`,
                duration: 3000
              });
            });
        })
        .catch(() => {
          // 用户点击取消
          console.log('[WorkReport] 用户取消提交');
        });
    },
    onProjectConfirm(value) {
      this.taskForm.project = value;
      this.showProjectPicker = false;
      
      // 更新任务名称选项
      this.taskNameOptions = this.projectTaskMap[value] || [];
      
      // 清空之前选择的任务名称
      this.taskForm.taskName = '';
    },
    onTaskNameConfirm(value) {
      this.taskForm.taskName = value;
      this.showTaskNamePicker = false;
      
      // 获取选中的任务对象
      const selectedTask = this.taskNameOptions.find(task => task.value === value);
      if (selectedTask) {
        // 自动填充工作内容
        this.taskForm.taskContent = selectedTask.description;
        // 设置预估工时
        this.taskForm.regularHours = selectedTask.estimatedHours;
      }
    },
    selectMonth(monthIndex) {
      const newDate = new Date(this.currentDate);
      newDate.setMonth(monthIndex);
      this.currentDate = newDate;
      // 选择月份后切换到月视图
      this.calendarMode = 'month';
    },
    goToApproval() {
      // 检查是否已经在目标路由
      if (this.$route.path === '/approval') {
        return;
      }
      
      // 防止快速重复点击
      if (this.navigating) {
        return;
      }
      
      this.navigating = true;
      this.$router.push('/approval')
        .catch(err => {
          // 忽略重复导航错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('导航到审批页面失败:', err);
          }
        })
        .finally(() => {
          this.navigating = false;
        });
    },
    goToStatistics() {
      // 检查是否已经在目标路由
      if (this.$route.path === '/statistics') {
        return;
      }
      
      // 防止快速重复点击
      if (this.navigating) {
        return;
      }
      
      this.navigating = true;
      this.$router.push('/statistics')
        .catch(err => {
          // 忽略重复导航错误
          if (err.name !== 'NavigationDuplicated') {
            console.error('导航到统计页面失败:', err);
          }
        })
        .finally(() => {
          this.navigating = false;
        });
    },
    viewTaskDetails(task) {
      // 设置当前查看的任务详情
      this.taskDetails = { ...task };
      // 显示任务详情弹窗
      this.showTaskDetails = true;

      // 获取任务详情专用的已报工时数据
      this.getTaskDetailsReportedHours(task);
    },
    editTaskFromDetails() {
      // 从详情页编辑任务
      console.log(`[WorkReport] 从详情页编辑任务: ID=${this.taskDetails.id}`);

      // 检查任务是否可编辑
      if (!this.canEditTask(this.taskDetails)) {
        console.log(`[WorkReport] 任务 ${this.taskDetails.id} 不可编辑，状态: ${this.taskDetails.status}`);
        this.$toast.fail('已提交或已审核的任务不可编辑');
        return;
      }

      // 关闭详情弹窗
      this.showTaskDetails = false;

      // 启动工作内容字段的内联编辑
      this.startEditing(this.taskDetails.id, 'taskContent');
    },
    closeTaskDetails() {
      this.showTaskDetails = false;
    },
    viewTaskItemDetails(task) {
      // 设置当前查看的任务详情
      this.currentItemDetails = {
        type: 'task',
        item: task,
        projectName: this.currentItemDetails.item.value
      };
      // 显示任务详情弹窗
      this.showItemDetails = true;
    },
    selectItemFromDetails() {
      // 处理选择项目或任务的逻辑
      if (this.currentItemDetails.type === 'project') {
        // 选择项目后的逻辑
        this.taskForm.project = this.currentItemDetails.item.value;
        this.taskNameOptions = this.projectTaskMap[this.currentItemDetails.item.value] || [];
        this.taskForm.taskName = '';
      } else {
        // 选择任务后的逻辑
        this.taskForm.taskName = this.currentItemDetails.item.value;
        this.taskForm.taskContent = this.currentItemDetails.item.description;
        this.taskForm.regularHours = this.currentItemDetails.item.estimatedHours;
        
        // 如果是从项目详情中选择的任务，需要设置项目
        if (this.currentItemDetails.projectName && !this.taskForm.project) {
          this.taskForm.project = this.currentItemDetails.projectName;
        }
      }
      this.showItemDetails = false;
      this.showTaskForm = true;
    },
    viewProjectDetails() {
      // 查找当前选中的项目
      const selectedProject = this.projectOptions.find(project => project.value === this.taskForm.project);
      if (selectedProject) {
        this.currentItemDetails = {
          type: 'project',
          item: selectedProject
        };
        this.showTaskForm = false;
        this.showItemDetails = true;
      }
    },
    viewProjectDetailsFromPicker(project) {
      this.currentItemDetails = {
        type: 'project',
        item: project
      };
      this.showProjectPicker = false;
      this.showItemDetails = true;
    },
    viewTaskNameDetails() {
      // 查找当前选中的任务
      const selectedTask = this.taskNameOptions.find(task => task.value === this.taskForm.taskName);
      if (selectedTask) {
        this.currentItemDetails = {
          type: 'task',
          item: selectedTask,
          projectName: this.taskForm.project
        };
        this.showTaskForm = false;
        this.showItemDetails = true;
      }
    },
    viewTaskNameDetailsFromPicker(task) {
      this.currentItemDetails = {
        type: 'task',
        item: task,
        projectName: this.taskForm.project
      };
      this.showTaskNamePicker = false;
      this.showItemDetails = true;
    },
    selectCombinedItem(item) {
      // 存储当前所有搜索结果到 taskForm
      this.taskForm.allSearchResults = [...this.combinedOptions];
      
      // 记录选择的项目/任务相关信息
      console.log(`[WorkReport] 选择${item.itemType === 'project' ? '项目' : '任务'}: ${item.displayText}`);
      console.log(`[WorkReport] 当前搜索结果数量: ${this.combinedOptions.length}`);
      
      if (item.itemType === 'project') {
        this.taskForm.project = item.value;  // 使用项目编码
        this.taskForm.projectName = item.text; // 保存项目名称
        this.taskNameOptions = this.projectTaskMap[item.value] || [];
        this.taskForm.taskName = '';
        this.taskForm.taskCode = ''; // 清除任务编码
        this.taskForm.isProjectLevel = false; // 重置项目级别标志
        
        // 添加项目计划工时处理
        if (item.plannedHours) {
          console.log(`[WorkReport] 项目"${item.text}"的计划工时: ${item.plannedHours}`);
          this.taskForm.plannedHours = item.plannedHours;
        } else {
          this.taskForm.plannedHours = 0;
        }
      } else {
        // 处理任务选择
        this.taskForm.taskName = item.text; // 保存任务名称
        this.taskForm.taskCode = item.value; // 使用任务编码
        this.taskForm.taskContent = item.description;
        this.taskForm.regularHours = item.estimatedHours;
        this.taskForm.plannedHours = item.plannedHours || item.estimatedHours;
        this.taskForm.startDate = item.startDate || '';
        this.taskForm.endDate = item.endDate || '';
        // 添加计划开始日期和计划结束日期字段
        this.taskForm.plannedStartDate = item.plannedStartDate || item.startDate || '';
        this.taskForm.plannedEndDate = item.plannedEndDate || item.endDate || '';
        this.taskForm.isProjectLevel = false; // 选择了具体任务，不是项目级别
        
        // 添加任务类型和所属项目字段
        this.taskForm.taskType = item.taskType || 'normal';
        this.taskForm.ownProject = item.ownProject || '';
        
        if (item.projectName && !this.taskForm.projectName) {
          this.taskForm.projectName = item.projectName;
        }
        
        if (item.projectValue && !this.taskForm.project) {
          this.taskForm.project = item.projectValue;
        }
      }
      
      this.showCombinedPicker = false;
      
      // 如果是添加新任务模式，直接创建新任务并开始内联编辑
      if (this.isAddingNewTask) {
        this.createNewTaskAndStartEditing();
      } else {
        // 原有逻辑：显示完整表单
      this.showTaskForm = true;
      }
    },
    viewCombinedItemDetails(item) {
      this.currentItemDetails = {
        type: item.itemType,
        item: item,
        projectName: item.projectName
      };
      this.showCombinedPicker = false;
      this.showItemDetails = true;
    },
    cancelCombinedPicker() {
      this.showCombinedPicker = false;
      
      // 如果是添加新任务模式，退出该模式并清空多选状态
      if (this.isAddingNewTask) {
        this.isAddingNewTask = false;
        this.isMultiSelectMode = false;
        this.selectedItems = [];
        console.log('[WorkReport] 用户取消添加新任务');
      }
    },
    confirmCombinedPicker() {
      // 如果是添加新任务模式，检查是否已选择项目/任务
      if (this.isAddingNewTask) {
        if (this.isMultiSelectMode) {
          // 多选模式：检查是否选择了项目
          if (this.selectedItems.length === 0) {
            this.$toast('请选择至少一个项目或任务');
            return;
          }
          
          // 批量创建任务
          this.createMultipleTasksAndStartEditing();
        } else {
          // 单选模式：保持原有逻辑
          if (!this.taskForm.project && !this.taskForm.taskCode) {
            this.$toast('请选择项目或任务');
            return;
          }
          
          // 创建单个任务
          this.createNewTaskAndStartEditing();
        }
      } else {
        // 原有逻辑：只是关闭弹窗
        this.showCombinedPicker = false;
      }
    },
    viewSelectedItemDetails() {
      if (this.taskForm.taskName) {
        // 查找当前选中的任务
        const selectedTask = this.taskNameOptions.find(task => task.value === this.taskForm.taskName);
        if (selectedTask) {
          this.currentItemDetails = {
            type: 'task',
            item: selectedTask,
            projectName: this.taskForm.project
          };
          this.showTaskForm = false;
          this.showItemDetails = true;
        }
      } else if (this.taskForm.project) {
        // 查找当前选中的项目
        const selectedProject = this.projectOptions.find(project => project.value === this.taskForm.project);
        if (selectedProject) {
          this.currentItemDetails = {
            type: 'project',
            item: selectedProject
          };
          this.showTaskForm = false;
          this.showItemDetails = true;
        }
      }
    },
    onSearchTypeChange(value) {
      this.searchType = value;
      this.filterType = value; // 保持同步
    },
    setFilterType(type) {
      this.filterType = type;
      this.searchType = type; // 保持同步
    },
    onPositionConfirm(value) {
      this.taskForm.position = value.value;           // 保存岗位编号/编码
      this.taskForm.positionName = value.text;        // 保存岗位名称
      console.log(`[WorkReport] 选择岗位: 编码=${value.value}, 名称=${value.text}`);
      this.showPositionPicker = false;
    },
    onProgressConfirm(value) {
      this.taskForm.progress = value.value;           // 保存进度数值
      console.log(`[WorkReport] 选择进度: ${value.value}%`);
      this.showProgressPicker = false;
    },
    
    // 日期选择器相关方法
    onStartDateConfirm(date) {
      this.taskForm.startDate = this.formatDate(date);
      this.showStartDatePicker = false;
      
      // 如果结束日期早于开始日期，则清空结束日期
      if (this.taskForm.endDate && this.taskForm.endDate < this.taskForm.startDate) {
        this.taskForm.endDate = '';
      }
    },
    
    onEndDateConfirm(date) {
      this.taskForm.endDate = this.formatDate(date);
      this.showEndDatePicker = false;
    },
    
    // 项目统计方法
    calculateProjectTotalPlannedHours(projectValue) {
      const projectTasks = this.projectTaskMap[projectValue] || [];
      return projectTasks.reduce((total, task) => {
        return total + (task.plannedHours || task.estimatedHours || 0);
      }, 0);
    },
    
    calculateProjectTotalReportedHours(projectValue) {
      // 使用API获取的真实已报工时数据
      const projectTasks = this.tasks.filter(task => task.project === projectValue);
      return projectTasks.reduce((total, task) => {
        return total + this.getTaskDisplayReportedHours(task);
      }, 0);
    },
    
    calculateProjectProgress(projectValue) {
      const projectTasks = this.projectTaskMap[projectValue] || [];
      if (projectTasks.length === 0) return 0;
      
      // 计算所有任务的进度平均值
      // 实际应用中可能需要加权平均（根据计划工时）
      const tasks = this.tasks.filter(task => task.project === projectValue);
      if (tasks.length === 0) return 0;
      
      const totalProgress = tasks.reduce((sum, task) => sum + (task.progress || 0), 0);
      return totalProgress / tasks.length;
    },
    
    // 展开/收起相关方法
    isItemExpanded(item) {
      if (!item || !item.itemType || !item.value) {
        return false;
      }
      const key = `${item.itemType}-${item.value}`;
      return this.expandedItemDetails[key] === true;
    },
    
    toggleItemExpanded(item) {
      if (!item || !item.itemType || !item.value) {
        return;
      }
      const key = `${item.itemType}-${item.value}`;
      this.$set(this.expandedItemDetails, key, !this.expandedItemDetails[key]);
    },
    
    // 获取任务已报工时
    getTaskReportedHours(item) {
      if (!item) return 0;
      const task = this.tasks.find(t => t.taskCode === item.value || t.taskName === item.text);
      if (task) {
        return this.getTaskDisplayReportedHours(task);
      }
      // 如果没有找到对应的任务，尝试直接用item的ID获取
      return this.getTaskDisplayReportedHours(item);
    },
    
    // 获取任务进度
    getTaskProgress(item) {
      if (!item) return 0;
      const task = this.tasks.find(t => t.taskCode === item.value || t.taskName === item.text);
      if (task) {
        return task.progress || 0;
      }
      return 0;
    },

    // 获取任务详情专用的已报工时数据
    async getTaskDetailsReportedHours(task) {
      if (!task) {
        console.warn('[WorkReport] 获取任务详情已报工时: 任务对象为空');
        return;
      }

      // 重置状态
      this.taskDetailsReportedHours = 0;
      this.taskDetailsReportedHoursLoading = true;
      this.taskDetailsReportedHoursError = null;

      try {
        // 使用与其他方法相同的ID选择逻辑
        let taskOrProjectId;
        if (task.ftaskId && task.ftaskId > 0) {
          taskOrProjectId = task.ftaskId;
          console.log('[WorkReport] 任务详情已报工时使用任务ID (ftaskId):', taskOrProjectId);
        } else if (task.fprojectId) {
          taskOrProjectId = task.fprojectId;
          console.log('[WorkReport] 任务详情已报工时使用项目ID (fprojectId):', taskOrProjectId);
        } else {
          // 回退到原有逻辑
          taskOrProjectId = task.taskId || task.taskCode || task.projectId || task.project;
          console.log('[WorkReport] 任务详情已报工时回退到原有逻辑，使用ID:', taskOrProjectId);
        }

        if (!taskOrProjectId) {
          console.warn('[WorkReport] 获取任务详情已报工时: 无法获取任务ID或项目ID');
          this.taskDetailsReportedHoursError = '无法获取任务标识';
          return;
        }

        console.log(`[WorkReport] 开始获取任务详情已报工时，ID: ${taskOrProjectId}, 任务名称: ${task.taskName}`);

        // 调用新的API获取已报工时
        const response = await getDueProjectTasks(taskOrProjectId, {
          timeout: 15000, // 15秒超时
          retries: 1 // 重试1次
        });

        console.log('[WorkReport] 任务详情已报工时API响应:', response);

        if (response.success && response.data) {
          this.taskDetailsReportedHours = response.data.reportedHours || 0;
          console.log(`[WorkReport] 成功获取任务详情已报工时: ${this.taskDetailsReportedHours}小时`);
        } else {
          console.warn('[WorkReport] 任务详情已报工时API返回失败:', response.message);
          this.taskDetailsReportedHoursError = response.message || '获取已报工时失败';
        }
      } catch (error) {
        console.error('[WorkReport] 获取任务详情已报工时失败:', error);
        this.taskDetailsReportedHoursError = error.message || '网络请求失败';
      } finally {
        this.taskDetailsReportedHoursLoading = false;
      }
    },

    // 获取任务详情已报工时的显示文本
    getTaskDetailsReportedHoursDisplay() {
      if (this.taskDetailsReportedHoursLoading) {
        return '加载中...';
      }

      if (this.taskDetailsReportedHoursError) {
        return `获取失败: ${this.taskDetailsReportedHoursError}`;
      }

      return this.taskDetailsReportedHours;
    },
    
    saveTasks() {
      // 检查是否正在保存或距离上次保存时间太短（小于2秒）
      const now = Date.now();
      if (this.isSaving || (now - this.lastSaveTime < 2000)) {
        console.log('[WorkReport] 已有保存操作在进行中或距离上次保存时间太短，请勿重复保存');
        this.$toast('已保存');
        return;
      }
      
      // 设置保存状态
      this.isSaving = true;
      
      // 将任务保存到本地存储
      try {
        // 确保每个任务都有日期字段
        const tasksWithDate = this.tasks.map(task => {
          if (!task.date) {
            task.date = this.formatDate(this.selectedDate);
          }
          return task;
        });

        // 对当前日期的任务进行去重处理
        const deduplicatedTasks = this.deduplicateTasksByName(tasksWithDate);
        if (deduplicatedTasks.length !== tasksWithDate.length) {
          console.log(`[WorkReport] 任务去重: 原始${tasksWithDate.length}个 -> 去重后${deduplicatedTasks.length}个`);
          // 更新当前显示的任务列表
          this.tasks = deduplicatedTasks;
        }

        // 从本地存储获取现有任务
        const existingTasksStr = localStorage.getItem('workReportTasks');
        let allTasks = [];

        if (existingTasksStr) {
          const existingTasks = JSON.parse(existingTasksStr);
          // 过滤掉当前日期的任务
          const otherDayTasks = existingTasks.filter(
            task => task.date !== this.formatDate(this.selectedDate)
          );
          // 合并其他日期的任务和当前日期的去重后任务
          allTasks = [...otherDayTasks, ...deduplicatedTasks];
        } else {
          allTasks = deduplicatedTasks;
        }
        
        // 保存到本地存储
        localStorage.setItem('workReportTasks', JSON.stringify(allTasks));
        
        // 更新最后保存时间
        this.lastSaveTime = now;
        
        console.log('任务已保存到本地存储');
      } catch (error) {
        console.error('保存任务失败:', error);
        this.$toast.fail('保存任务失败');
      } finally {
        // 重置保存状态
        this.isSaving = false;
      }
    },
    
    // 格式化任务状态的方法
    formatTaskStatus(status) {
      if (!status) return '未知状态';
      
      // 将状态码转换为小写
      const lowerStatus = status.toLowerCase();
      
      // 状态代码映射说明：
      // 'a' - 暂存状态（新创建的任务默认状态）
      // 'b' - 已提交状态（任务提交后的状态）
      // 'c' - 已审核状态（审批通过后的状态）
      // 'd' - 重新审核状态（驳回后需要重新审核的状态）- 现在合并到暂存状态
      
      // 根据API返回的实际状态码映射
      switch(lowerStatus) {
        case 'a':
          return '暂存';
        case 'b':
          return '已提交';
        case 'c':
          return '已审核';
        case 'd':
          return '暂存'; // 将重新审核状态合并到暂存状态显示
        default:
          return '未知状态';
      }
    },
    // 根据状态获取样式类
    getStatusClass(status) {
      if (!status) return 'status-draft';
      
      // 将状态码转换为小写
      const lowerStatus = status.toLowerCase();
      
      switch(lowerStatus) {
        case 'a': // 'a' 对应 "暂存"
          return 'status-draft';
        case 'b': // 'b' 对应 "已提交"
          return 'status-submitted';
        case 'c': // 'c' 对应 "已审核"
          return 'status-approved';
        case 'd': // 'd' 对应 "重新审核" - 现在使用与暂存相同的样式
          return 'status-draft'; // 将重新审核状态合并到暂存状态样式
        default:
          return 'status-draft';
      }
    },
    // 验证任务状态，确保所有任务都有有效的status值
    validateTaskStatus() {
      if (!this.tasks || this.tasks.length === 0) {
        return;
      }
      
      console.log('[WorkReport] 验证任务状态，当前任务数量:', this.tasks.length);
      let fixedCount = 0;
      
      // 遍历任务，验证并修复status值
      this.tasks = this.tasks.map(task => {
        // 记录原始状态值供日志使用
        const originalStatus = task.status;
        
        // 将状态码转换为小写（如果存在）
        if (task.status) {
          task.status = task.status.toLowerCase();
        }
        
        // 验证状态码是否有效
        if (!task.status || !['a', 'b', 'c', 'd'].includes(task.status)) {
          fixedCount++;
          // 如果状态码无效，则使用默认值
          const fixedTask = { ...task, status: 'a' }; // 默认为"暂存"(状态代码'a')
          
          // 记录状态修复
          if (originalStatus) {
            console.log(`[WorkReport] 修复无效状态: 原始='${originalStatus}', 修复后='a'`);
          }
          
          return fixedTask;
        }
        
        // 如果状态码有效但进行了小写转换，记录转换
        if (originalStatus !== task.status) {
          console.log(`[WorkReport] 标准化状态: 原始='${originalStatus}', 标准化='${task.status}'`);
        }
        
        return task;
      });
      
      if (fixedCount > 0) {
        console.log(`[WorkReport] 已修复 ${fixedCount} 个任务的状态值`);
      } else {
        console.log('[WorkReport] 所有任务状态值正常');
      }
      
      // 输出当前任务状态统计
      const counts = { draft: 0, submitted: 0, approved: 0 };
      this.tasks.forEach(task => {
        if (task.status === 'a') counts.draft++;      // 'a' 对应 "暂存"
        else if (task.status === 'b') counts.submitted++; // 'b' 对应 "已提交"
        else if (task.status === 'c') counts.approved++;  // 'c' 对应 "已审核"
        else if (task.status === 'd') counts.reaudit++;   // 'd' 对应 "重新审核"
      });
      console.log(`[WorkReport] 当前任务状态统计: 暂存=${counts.draft}, 已提交=${counts.submitted}, 已审核=${counts.approved}`);
    },
    // 加载项目和任务数据
    loadProjectAndTaskData() {
      // 从localStorage获取用户信息
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const userId = userInfo.userEntityId || userInfo.userId; // 优先使用FUserId，后备使用Fid
      
      // 打印完整的用户信息用于调试
      console.log('[WorkReport] 当前用户信息:', JSON.stringify(userInfo, null, 2));
      console.log('[WorkReport] 用户ID信息 - userEntityId:', userInfo.userEntityId, 'userId:', userInfo.userId, '最终使用的userId:', userId);
      console.log(`[WorkReport] 用户ID: ${userId}, 用户名: ${userInfo.userName || '未设置'}, 用户账号: ${userInfo.username || '未设置'}`);
      
      if (!userId) {
        console.error('[WorkReport] 加载项目和任务数据失败: 未找到用户ID');
        this.$toast.fail('用户信息不完整，请重新登录');
        return;
      }
      
      this.$toast.loading({
        message: '加载项目/任务数据...',
        forbidClick: true
      });
      
      console.log(`[WorkReport] 开始加载项目和任务数据, 用户ID: ${userId}`);
      
      // 添加请求开始时间记录
      const startTime = Date.now();
      
      getProjectAndTaskData(userId, { forceRefresh: true })
        .then(response => {
          this.$toast.clear();
          
          // 计算请求耗时
          const endTime = Date.now();
          const duration = endTime - startTime;
          console.log(`[WorkReport] 项目和任务数据请求完成, 耗时: ${duration}ms`);
          
          if (response.success) {
            this.projectOptions = response.data.projectOptions;
            this.projectTaskMap = response.data.projectTaskMap;
            
            // 输出加载的项目和任务数量
            const projectCount = this.projectOptions.length;
            let taskCount = 0;
            Object.values(this.projectTaskMap).forEach(tasks => {
              taskCount += tasks.length;
            });
            
            console.log(`[WorkReport] 项目和任务数据加载成功: ${projectCount}个项目, ${taskCount}个任务`);
            
            // 检查是否有默认项目（未分类任务）
            const defaultProject = this.projectOptions.find(p => p.value === 'unassigned_tasks');
            if (defaultProject) {
              console.log('[WorkReport] 检测到默认项目:', JSON.stringify(defaultProject, null, 2));
              const defaultTasks = this.projectTaskMap['unassigned_tasks'] || [];
              console.log(`[WorkReport] 默认项目中的任务数量: ${defaultTasks.length}`);
              if (defaultTasks.length > 0) {
                console.log('[WorkReport] 默认项目中的第一个任务:', JSON.stringify(defaultTasks[0], null, 2));
              }
            }
            
            // 打印所有项目和任务映射的简要统计
            console.log('[WorkReport] 项目和任务分布统计:');
            Object.entries(this.projectTaskMap).forEach(([projectCode, tasks]) => {
              const projectName = this.projectOptions.find(p => p.value === projectCode)?.text || '未知项目';
              console.log(`  项目 "${projectName}" (${projectCode}): ${tasks.length}个任务`);
            });
            
            // 打印项目选项示例
            if (this.projectOptions.length > 0) {
              console.log('[WorkReport] 项目选项示例:', this.projectOptions.slice(0, 3));
            } else {
              console.warn('[WorkReport] 警告: 未加载到任何项目数据');
            }
            
            // 打印示例数据，帮助开发调试
            console.log('[WorkReport] 加载项目和任务数据示例:');
            if (this.projectOptions.length > 0) {
              console.log('[WorkReport] 第一个项目:', JSON.stringify(this.projectOptions[0], null, 2));
              
              // 检查项目中的FOWNPROJECT字段
              const projectsWithOwnProject = this.projectOptions.filter(p => p.ownProject).length;
              if (projectsWithOwnProject > 0) {
                console.log(`[WorkReport] 发现 ${projectsWithOwnProject} 个项目有所属项目字段(ownProject)`);
                const exampleWithOwnProject = this.projectOptions.find(p => p.ownProject);
                if (exampleWithOwnProject) {
                  console.log('[WorkReport] 带有所属项目的示例:', JSON.stringify(exampleWithOwnProject, null, 2));
                }
              } else {
                console.log('[WorkReport] 未发现项目具有所属项目字段(ownProject)');
              }
            } else {
              console.warn('[WorkReport] 警告: 未加载到任何项目数据');
            }
            
            const firstProjectCode = Object.keys(this.projectTaskMap)[0];
            if (firstProjectCode && this.projectTaskMap[firstProjectCode].length > 0) {
              console.log(`[WorkReport] 项目 "${firstProjectCode}" 的第一个任务:`, 
                JSON.stringify(this.projectTaskMap[firstProjectCode][0], null, 2));
              
              // 检查任务中的FOWNPROJECT字段
              let tasksWithOwnProject = 0;
              Object.values(this.projectTaskMap).forEach(tasks => {
                tasksWithOwnProject += tasks.filter(t => t.ownProject).length;
              });
              
              if (tasksWithOwnProject > 0) {
                console.log(`[WorkReport] 发现 ${tasksWithOwnProject} 个任务有所属项目字段(ownProject)`);
                
                // 查找并显示每种匹配类型的示例任务
                const matchTypes = ['ownproject', 'exact-name', 'case-insensitive-name', 'fuzzy-name-match', 'id'];
                matchTypes.forEach(matchType => {
                  let found = false;
                  for (const tasks of Object.values(this.projectTaskMap)) {
                    const matchingTask = tasks.find(t => t.matchType === matchType);
                    if (matchingTask) {
                      console.log(`[WorkReport] 匹配类型"${matchType}"的示例任务:`, JSON.stringify(matchingTask, null, 2));
                      found = true;
                      break;
                    }
                  }
                  if (!found) {
                    console.log(`[WorkReport] 未找到匹配类型"${matchType}"的任务`);
                  }
                });
              } else {
                console.log('[WorkReport] 未发现任务具有所属项目字段(ownProject)');
              }
            } else {
              console.warn('[WorkReport] 警告: 未加载到任何任务数据');
            }
          } else {
            console.warn('[WorkReport] 加载项目和任务数据返回失败状态:', response.message);
            this.$toast.fail(response.message || '加载项目和任务数据失败');
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('[WorkReport] 加载项目和任务数据失败:', error);
          console.error('[WorkReport] 错误详情:', error.stack);
          this.$toast.fail(`加载失败: ${error.message}`);
        });
    },
    
    // 添加刷新项目和任务数据的方法
    refreshProjectAndTaskData() {
      // 从localStorage获取用户信息
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const userId = userInfo.userEntityId || userInfo.userId; // 优先使用FUserId，后备使用Fid
      
      // 打印完整的用户信息用于调试
      console.log('[WorkReport] 强制刷新 - 当前用户信息:', JSON.stringify(userInfo, null, 2));
      console.log('[WorkReport] 强制刷新 - 用户ID信息 - userEntityId:', userInfo.userEntityId, 'userId:', userInfo.userId, '最终使用的userId:', userId);
      console.log(`[WorkReport] 强制刷新 - 用户ID: ${userId}, 用户名: ${userInfo.userName || '未设置'}, 用户账号: ${userInfo.username || '未设置'}`);
      
      if (!userId) {
        console.error('[WorkReport] 刷新项目和任务数据失败: 未找到用户ID');
        this.$toast.fail('用户信息不完整，请重新登录');
        return;
      }
      
      this.$toast.loading({
        message: '强制刷新项目/任务数据...',
        forbidClick: true
      });
      
      console.log(`[WorkReport] 开始强制刷新项目和任务数据, 用户ID: ${userId}`);
      
      // 添加请求开始时间记录
      const startTime = Date.now();
      
      // 使用forceRefresh选项强制刷新数据
      getProjectAndTaskData(userId, { forceRefresh: true })
        .then(response => {
          this.$toast.clear();
          
          // 计算请求耗时
          const endTime = Date.now();
          const duration = endTime - startTime;
          console.log(`[WorkReport] 强制刷新 - 项目和任务数据请求完成, 耗时: ${duration}ms`);
          
          if (response.success) {
            this.projectOptions = response.data.projectOptions;
            this.projectTaskMap = response.data.projectTaskMap;
            
            // 输出刷新的项目和任务数量
            const projectCount = this.projectOptions.length;
            let taskCount = 0;
            Object.values(this.projectTaskMap).forEach(tasks => {
              taskCount += tasks.length;
            });
            
            console.log(`[WorkReport] 项目和任务数据强制刷新成功: ${projectCount}个项目, ${taskCount}个任务`);
            this.$toast.success('项目和任务数据已刷新');
            
            // 打印示例数据，帮助开发调试
            console.log('[DEV] 强制刷新后的项目和任务数据示例:');
            if (this.projectOptions.length > 0) {
              console.log('[DEV] 第一个项目:', JSON.stringify(this.projectOptions[0], null, 2));
            } else {
              console.warn('[DEV] 警告: 未加载到任何项目数据');
            }
            
            const firstProjectCode = Object.keys(this.projectTaskMap)[0];
            if (firstProjectCode && this.projectTaskMap[firstProjectCode].length > 0) {
              console.log(`[DEV] 项目 "${firstProjectCode}" 的第一个任务:`, 
                JSON.stringify(this.projectTaskMap[firstProjectCode][0], null, 2));
            } else {
              console.warn('[DEV] 警告: 未加载到任何任务数据');
            }
            
            // 打印项目数据结构详情
            if (projectCount > 0) {
              const firstProject = this.projectOptions[0];
              console.log('[DEV] 项目数据结构:', Object.keys(firstProject).join(', '));
              console.log('[DEV] 第一个项目数据详情:', JSON.stringify(firstProject, null, 2));
            }
            
            // 打印任务数据结构详情
            if (firstProjectCode && this.projectTaskMap[firstProjectCode].length > 0) {
              const firstTask = this.projectTaskMap[firstProjectCode][0];
              console.log('[DEV] 任务数据结构:', Object.keys(firstTask).join(', '));
              console.log('[DEV] 第一个任务数据详情:', JSON.stringify(firstTask, null, 2));
            }
            
            // 检查是否有默认项目（未分类任务）
            const defaultProject = this.projectOptions.find(p => p.value === 'unassigned_tasks');
            if (defaultProject) {
              console.log('[DEV] 检测到默认项目:', JSON.stringify(defaultProject, null, 2));
              const defaultTasks = this.projectTaskMap['unassigned_tasks'] || [];
              console.log(`[DEV] 默认项目中的任务数量: ${defaultTasks.length}`);
              if (defaultTasks.length > 0) {
                console.log('[DEV] 默认项目中的第一个任务:', JSON.stringify(defaultTasks[0], null, 2));
              }
            }
            
            // 打印所有项目和任务映射的简要统计
            console.log('[DEV] 项目和任务分布统计:');
            Object.entries(this.projectTaskMap).forEach(([projectCode, tasks]) => {
              const projectName = this.projectOptions.find(p => p.value === projectCode)?.text || '未知项目';
              console.log(`  项目 "${projectName}" (${projectCode}): ${tasks.length}个任务`);
            });
          } else {
            console.warn('[WorkReport] 刷新项目和任务数据返回失败状态:', response.message);
            this.$toast.fail(response.message || '刷新项目和任务数据失败');
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('[WorkReport] 刷新项目和任务数据失败:', error);
          console.error('[WorkReport] 错误详情:', error.stack);
          this.$toast.fail(`刷新失败: ${error.message}`);
        });
    },
    // 加载岗位数据
    loadPositionData() {
      // 从localStorage获取用户信息
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const username = userInfo.username || '';
      
      // 打印完整的用户信息用于调试
      console.log('[WorkReport] 当前用户信息:', JSON.stringify(userInfo, null, 2));
      console.log(`[WorkReport] 用户账号: ${username || '未设置'}`);
      
      if (!username) {
        console.warn('[WorkReport] 用户账号不存在，使用默认岗位列表');
        return;
      }
      
      this.$toast.loading({
        message: '加载岗位数据...',
        forbidClick: true
      });
      
      console.log(`[WorkReport] 开始加载岗位数据, 用户账号: ${username}`);
      
      // 添加请求开始时间记录
      const startTime = Date.now();
      
      getPositionData(username, { forceRefresh: true })
        .then(response => {
          this.$toast.clear();
          
          // 计算请求耗时
          const endTime = Date.now();
          const duration = endTime - startTime;
          console.log(`[WorkReport] 岗位数据请求完成, 耗时: ${duration}ms`);
          
          if (response.success) {
            // 过滤掉"其他"岗位选项
            const filteredPositions = response.data.filter(position => 
              position.text !== '其他' && position.value !== '其他'
            );
            
            this.positionOptions = filteredPositions;
            console.log(`[WorkReport] 岗位数据加载成功, 原始数量: ${response.data.length}, 过滤后数量: ${this.positionOptions.length}`);
            
            // 打印岗位选项示例
            if (this.positionOptions.length > 0) {
              console.log('[WorkReport] 岗位选项示例:', this.positionOptions.slice(0, 3));
            }
            
            // 将完整的岗位选项数据以JSON格式打印到控制台
            console.log('[WorkReport] 完整岗位选项数据 (JSON格式):');
            console.log(JSON.stringify(this.positionOptions, null, 2));
            
            // 如果只有一个岗位，自动设置默认岗位
            if (this.positionOptions.length === 1) {
              const singlePosition = this.positionOptions[0];
              console.log(`[WorkReport] 检测到只有一个岗位，自动设置为默认岗位: ${singlePosition.text}`);
              
              // 为新任务表单设置默认岗位
              this.taskForm.position = singlePosition.value;
              this.taskForm.positionName = singlePosition.text;
              
              // 为现有任务设置默认岗位（如果没有岗位信息）
              this.tasks.forEach(task => {
                if (!task.position && !task.positionName) {
                  task.position = singlePosition.value;
                  task.positionName = singlePosition.text;
                }
              });
              
              this.saveTasks();
              this.$toast.success(`已自动设置岗位为: ${singlePosition.text}`);
            }
            
          } else {
            console.warn('[WorkReport] 加载岗位数据返回失败状态:', response.message);
            this.$toast.fail(response.message || '加载岗位数据失败');
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('[WorkReport] 加载岗位数据失败:', error);
          console.error('[WorkReport] 错误详情:', error.stack);
          this.$toast.fail(`加载失败: ${error.message}`);
        });
    },
    // 加载耗费类型数据
    loadBurnOffTypeData() {
      this.$toast.loading({
        message: '加载耗费类型数据...',
        forbidClick: true
      });
      
      console.log(`[WorkReport] 开始加载耗费类型数据`);
      
      // 添加请求开始时间记录
      const startTime = Date.now();
      
      getBurnOffTypeData({ forceRefresh: true })
        .then(response => {
          this.$toast.clear();
          
          // 计算请求耗时
          const endTime = Date.now();
          const duration = endTime - startTime;
          console.log(`[WorkReport] 耗费类型数据请求完成, 耗时: ${duration}ms`);
          
          if (response.success) {
            this.burnOffTypeOptions = response.data;
            console.log(`[WorkReport] 耗费类型数据加载成功, 数量: ${this.burnOffTypeOptions.length}`);
            
            // 打印耗费类型选项示例
            if (this.burnOffTypeOptions.length > 0) {
              console.log('[WorkReport] 耗费类型选项示例:', this.burnOffTypeOptions.slice(0, 3));
            }
            
            // 将完整的耗费类型选项数据以JSON格式打印到控制台
            console.log('[WorkReport] 完整耗费类型选项数据 (JSON格式):');
            console.log(JSON.stringify(this.burnOffTypeOptions, null, 2));
          } else {
            console.warn('[WorkReport] 加载耗费类型数据返回失败状态:', response.message);
            this.$toast.fail(response.message || '加载耗费类型数据失败');
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('[WorkReport] 加载耗费类型数据失败:', error);
          console.error('[WorkReport] 错误详情:', error.stack);
          this.$toast.fail(`加载失败: ${error.message}`);
        });
    },
    // 加载临时任务类型数据
    loadTmpTaskTypeData() {
      this.$toast.loading({
        message: '加载临时任务类型数据...',
        forbidClick: true
      });
      
      console.log(`[WorkReport] 开始加载临时任务类型数据`);
      
      // 添加请求开始时间记录
      const startTime = Date.now();
      
      getTmpTaskTypeData({ forceRefresh: true })
        .then(response => {
          this.$toast.clear();
          
          // 计算请求耗时
          const endTime = Date.now();
          const duration = endTime - startTime;
          console.log(`[WorkReport] 临时任务类型数据请求完成, 耗时: ${duration}ms`);
          
          if (response.success) {
            this.tmpTaskTypeOptions = response.data;
            console.log(`[WorkReport] 临时任务类型数据加载成功, 数量: ${this.tmpTaskTypeOptions.length}`);
            
            // 打印临时任务类型选项示例
            if (this.tmpTaskTypeOptions.length > 0) {
              console.log('[WorkReport] 临时任务类型选项示例:', this.tmpTaskTypeOptions.slice(0, 3));
            }
            
            // 将完整的临时任务类型选项数据以JSON格式打印到控制台
            console.log('[WorkReport] 完整临时任务类型选项数据 (JSON格式):');
            console.log(JSON.stringify(this.tmpTaskTypeOptions, null, 2));
          } else {
            console.warn('[WorkReport] 加载临时任务类型数据返回失败状态:', response.message);
            this.$toast.fail(response.message || '加载临时任务类型数据失败');
          }
        })
        .catch(error => {
          this.$toast.clear();
          console.error('[WorkReport] 加载临时任务类型数据失败:', error);
          console.error('[WorkReport] 错误详情:', error.stack);
          this.$toast.fail(`加载失败: ${error.message}`);
        });
    },
    /**
     * 处理FBillno的提取、存储和打印
     * @param {Object} response API响应数据
     * @param {string} source 来源标识，用于日志区分
     */
    handleFBillno(response, targetTaskId = null) {
      if (!response || !response.data) {
        console.log('[WorkReport] 无响应数据，跳过FBillno处理');
        return;
      }
      
      console.log('[WorkReport] 处理FBillno - 来源:', response.data);
      
      // 处理保存响应
      if (response.data.Result && response.data.Result.ResponseStatus && response.data.Result.ResponseStatus.IsSuccess) {
        // 从保存响应中提取FBillno
        const saveResult = response.data.Result;
        const fbillno = saveResult.Number || saveResult.Id || '';
        
        if (fbillno) {
          console.log(`[WorkReport] 从保存响应中提取FBillno: ${fbillno}`);
          
          // 更新任务中的fbillno字段
          if (this.tasks && this.tasks.length > 0) {
            // 更新指定任务或第一个没有fbillno的任务
            const updatedTasks = this.tasks.map(task => {
              if (targetTaskId) {
                // 如果指定了targetTaskId，只更新该任务
                if (task.id === targetTaskId) {
                  if (!task.fbillno) {
                    console.log(`[WorkReport] 为指定任务ID=${task.id}添加fbillno=${fbillno}`);
                    return {
                      ...task,
                      fbillno: fbillno
                    };
                  } else {
                    console.log(`[WorkReport] 指定任务ID=${task.id}已有fbillno=${task.fbillno}，跳过设置`);
                  }
                }
                return task;
              } else {
                // 如果没有指定targetTaskId，更新第一个没有fbillno的任务
                if (!task.fbillno) {
                  console.log(`[WorkReport] 为任务ID=${task.id}添加fbillno=${fbillno}`);
                  return {
                    ...task,
                    fbillno: fbillno
                  };
                }
                return task;
              }
            });
            
            // 更新任务列表
            this.tasks = updatedTasks;
            
            // 保存更新后的任务列表到本地存储
            this.saveTasks();
          }
          
          // 存储到Vuex Store
          this.$store.dispatch('saveFBillno', fbillno);
        } else {
          console.log('[WorkReport] 保存响应中未找到FBillno');
        }
      }
      // 处理提交响应
      else if (response.data.Result && response.data.Result.ResponseStatus && response.data.Result.ResponseStatus.SuccessEntitys) {
        // 从提交响应中提取FBillno
        const successEntities = response.data.Result.ResponseStatus.SuccessEntitys;
        if (Array.isArray(successEntities) && successEntities.length > 0) {
          const fbillnos = successEntities.map(entity => entity.Number).filter(Boolean);
          
          if (fbillnos.length > 0) {
            console.log(`[WorkReport] 从提交响应中提取FBillno列表: ${fbillnos.join(', ')}`);
            
            // 更新任务中的fbillno字段
            if (this.tasks && this.tasks.length > 0) {
              // 对于提交成功的任务，更新其fbillno
              const updatedTasks = this.tasks.map(task => {
                // 如果任务状态为"已提交"但没有fbillno，则添加第一个fbillno
                if (task.status === 'b' && !task.fbillno && fbillnos.length > 0) {
                  console.log(`[WorkReport] 为已提交任务ID=${task.id}添加fbillno=${fbillnos[0]}`);
                  return {
                    ...task,
                    fbillno: fbillnos[0]
                  };
                }
                return task;
              });
              
              // 更新任务列表
              this.tasks = updatedTasks;
              
              // 保存更新后的任务列表到本地存储
              this.saveTasks();
            }
            
            // 存储到Vuex Store（存储第一个fbillno）
            if (fbillnos.length > 0) {
              this.$store.dispatch('saveFBillno', fbillnos[0]);
            }
          } else {
            console.log('[WorkReport] 提交响应中未找到FBillno');
          }
        }
      } else {
        console.log('[WorkReport] 响应数据格式不符合预期，无法提取FBillno');
      }
    },
    canDeleteTask(task) {
      // 检查任务状态，只允许删除状态为'a'(暂存)或'd'(驳回)的任务
      if (!task || !task.status) {
        console.warn('[WorkReport] canDeleteTask: 任务或状态信息缺失', task);
        return false;
      }

      // 将状态转换为小写进行比较
      const status = (task.status || '').toLowerCase();
      const canDelete = ['a', 'd'].includes(status);

      console.log(`[WorkReport] canDeleteTask: 任务ID=${task.id}, 状态=${status}, 可删除=${canDelete}`);

      return canDelete;
    },
    canEditTask(task) {
      // 检查任务状态，只允许编辑状态为'a'(暂存)或'd'(驳回)的任务
      // 已提交('b')和已审核('c')的任务不可编辑
      if (!task || !task.status) {
        console.warn('[WorkReport] canEditTask: 任务或状态信息缺失', task);
        return false;
      }

      // 将状态转换为小写进行比较
      const status = (task.status || '').toLowerCase();
      const canEdit = ['a', 'd'].includes(status);

      console.log(`[WorkReport] canEditTask: 任务ID=${task.id}, 状态=${status}, 可编辑=${canEdit}`);

      return canEdit;
    },
    
    /**
     * 计算每个日期的工时状态
     * @param {Object} dateWorkHourMap 按日期分组的工时数据
     * @returns {Object} 日期到状态的映射
     */
    calculateDateWorkHourStatus(dateWorkHourMap) {
      const statusMap = {};
      
      if (!dateWorkHourMap || typeof dateWorkHourMap !== 'object') {
        console.warn('[WorkReport] calculateDateWorkHourStatus: 无效的工时数据');
        return statusMap;
      }
      
      // 获取今天的日期字符串 (格式: YYYY-MM-DD)
      const today = new Date();
      const todayStr = today.getFullYear() + '-' + 
                      String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                      String(today.getDate()).padStart(2, '0');
      
      console.log(`[WorkReport] 计算工时状态，当前日期: ${todayStr}，只处理今天及以前的数据`);
      
      // 遍历每个日期的工时数据
      Object.keys(dateWorkHourMap).forEach(dateKey => {
        const dateData = dateWorkHourMap[dateKey];
        
        // 检查日期是否在今天及以前
        if (dateKey > todayStr) {
          console.log(`[WorkReport] 跳过未来日期: ${dateKey}`);
          return; // 跳过未来日期
        }
        
        if (!dateData || !dateData.records || dateData.records.length === 0) {
          // 无数据时，检查是否为周末或节假日
          if (this.isWeekendOrHoliday(dateKey)) {
            // 周末或节假日无数据时不显示任何指示器（周末不要求工作）
            statusMap[dateKey] = null;
            console.log(`[WorkReport] 日期 ${dateKey}: 周末/节假日无数据，不显示指示器（周末不要求工作）`);
          } else {
            // 工作日无数据时显示红点
            statusMap[dateKey] = 'none';
            console.log(`[WorkReport] 日期 ${dateKey}: 工作日无数据，显示红点`);
          }
          return;
        }
        
        // 检查总工时是否满8小时
        const totalHours = parseFloat(dateData.totalHours) || 0;
        const isWeekend = this.isWeekendOrHoliday(dateKey);
        
        if (totalHours >= 8) {
          // 工时满8小时显示对勾（包括周末）
          statusMap[dateKey] = 'approved';
          console.log(`[WorkReport] 日期 ${dateKey}: 工时满足(${totalHours}h >= 8h)，显示对勾${isWeekend ? '（周末）' : ''}`);
        } else {
          // 工时不足8小时时，检查是否为周末或节假日
          if (isWeekend) {
            // 周末或节假日工时不足时仍显示对勾（有工作记录就显示）
            statusMap[dateKey] = 'approved';
            console.log(`[WorkReport] 日期 ${dateKey}: 周末/节假日有工作记录(${totalHours}h)，显示对勾`);
          } else {
            // 工作日工时不足时显示红点
            statusMap[dateKey] = 'none';
            console.log(`[WorkReport] 日期 ${dateKey}: 工作日工时不足(${totalHours}h < 8h)，显示红点`);
          }
        }
      });
      
      console.log(`[WorkReport] 计算完成，共处理 ${Object.keys(statusMap).length} 个日期的状态`);
      return statusMap;
    },
    
    /**
     * 刷新日历状态数据
     */
    async refreshCalendarStatus() {
      try {
        console.log('[WorkReport] 开始刷新日历状态数据');
        
        // 获取用户账号
        const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
        const userAccount = userInfo.username;
        
        if (!userAccount) {
          console.warn('[WorkReport] 刷新日历状态失败：未找到用户账号');
          return;
        }
        
        // 调用API获取工时状态数据
        const { getCalendarWorkHourStatus } = await import('../api/report.js');
        const response = await getCalendarWorkHourStatus(userAccount);
        
        if (response.success) {
          // 计算日期状态映射
          this.dateStatusMap = this.calculateDateWorkHourStatus(response.data.dateWorkHourMap);
          
          // 触发日历重新渲染
          this.$nextTick(() => {
            this.generateCalendarDays();
          });
          
          console.log('[WorkReport] 日历状态数据刷新成功');
        } else {
          console.warn('[WorkReport] 获取日历状态数据失败:', response.message);
        }
      } catch (error) {
        console.error('[WorkReport] 刷新日历状态数据异常:', error);
      }
    },
    
    /**
     * 强制刷新日历状态，立即生效
     */
    async forceRefreshCalendarStatus() {
      try {
        console.log('[WorkReport] 强制刷新日历状态');
        
        // 清除现有的状态缓存
        this.dateStatusMap = {};
        
        // 重新计算状态（即使没有API数据，也会根据周末节假日判断）
        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();
        
        // 计算当前月份的所有日期状态
        const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
        for (let day = 1; day <= daysInMonth; day++) {
          const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
          // 调用getDateTaskStatus会自动处理周末节假日逻辑
          this.dateStatusMap[dateStr] = this.getDateTaskStatus(dateStr);
        }
        
        // 重新获取API数据
        await this.refreshCalendarStatus();
        
        console.log('[WorkReport] 强制刷新完成');
        
      } catch (error) {
        console.error('[WorkReport] 强制刷新日历状态失败:', error);
      }
    },
    // 表格编辑相关方法
    toggleDisplayMode() {
      this.displayMode = this.displayMode === 'table' ? 'card' : 'table';
      console.log(`[WorkReport] 切换显示模式为: ${this.displayMode}`);
      
      // 保存用户的视图偏好
      localStorage.setItem('workReportDisplayMode', this.displayMode);
      console.log(`[WorkReport] 已保存用户视图偏好: ${this.displayMode}`);
    },
    
    startEditing(taskId, field) {
      console.log(`[WorkReport] 开始编辑任务 ${taskId} 的字段 ${field}`);

      // 检查任务是否可编辑
      const task = this.tasks.find(t => t.id === taskId);
      if (!this.canEditTask(task)) {
        console.log(`[WorkReport] 任务 ${taskId} 不可编辑，状态: ${task?.status}`);
        this.$toast.fail('已提交或已审核的任务不可编辑');
        return;
      }

      this.editingTaskId = taskId;
      this.editingField = field;
      
      // 下一个tick自动聚焦输入框
      this.$nextTick(() => {
        try {
        const editingInputs = this.$refs.editingInput;
          
          if (editingInputs) {
            let targetInput = null;
            
            // 如果是数组，取第一个
            if (Array.isArray(editingInputs)) {
              targetInput = editingInputs[0];
            } else {
              targetInput = editingInputs;
            }
            
            if (targetInput) {
              // 对于工时字段，如果值为0或空，将输入框设置为空
              if ((field === 'regularHours' || field === 'overtimeHours') && 
                  (!task[field] || task[field] === 0)) {
                // 延迟设置空值，确保DOM已更新
                this.$nextTick(() => {
                  if (targetInput.value !== undefined) {
                    targetInput.value = '';
                  }
                });
              }
              
              // 对于 van-field 组件，尝试聚焦内部的 input 元素
              if (targetInput.$el && targetInput.$el.querySelector) {
                const inputElement = targetInput.$el.querySelector('input') || targetInput.$el.querySelector('textarea');
                if (inputElement && typeof inputElement.focus === 'function') {
                  inputElement.focus();
                  // 添加延迟滚动，确保键盘弹出后再滚动
                  setTimeout(() => {
                    this.scrollToInput(inputElement);
                  }, 300);
                  console.log(`[WorkReport] 成功聚焦到字段 ${field} 的输入元素`);
                }
              } 
              // 如果是原生 DOM 元素或 Vue 组件，尝试调用 focus 方法
              else if (targetInput.focus && typeof targetInput.focus === 'function') {
                targetInput.focus();
                // 添加延迟滚动，确保键盘弹出后再滚动
                setTimeout(() => {
                  this.scrollToInput(targetInput);
                }, 300);
                console.log(`[WorkReport] 成功聚焦到字段 ${field} 的元素`);
              }
            }
          }
        } catch (error) {
          console.warn(`[WorkReport] 聚焦字段 ${field} 时出错:`, error);
          // 聚焦失败不影响编辑功能，只是用户体验稍差
        }
      });
    },
    
    stopEditing() {
      console.log(`[WorkReport] 停止编辑任务 ${this.editingTaskId} 的字段 ${this.editingField}`);
      
      // 保存修改的数据
      if (this.editingTaskId && this.editingField) {
        const taskIndex = this.tasks.findIndex(task => task.id === this.editingTaskId);
        if (taskIndex !== -1) {
          const task = this.tasks[taskIndex];
          
          // 验证数据
          if (this.editingField === 'regularHours' || this.editingField === 'overtimeHours') {
            let value = task[this.editingField];
            
            // 处理空字符串或空值
            if (value === '' || value === null || value === undefined) {
              value = 0;
            } else {
              value = parseFloat(value);
            }
            
            if (isNaN(value) || value < 0 || value > 24) {
              this.$toast.fail('工时必须在0-24小时之间');
              return;
            }
            task[this.editingField] = value;
          } else if (this.editingField === 'progress') {
            const value = parseFloat(task[this.editingField]);
            if (isNaN(value) || value < 0 || value > 100) {
              this.$toast.fail('进度必须在0-100%之间');
              return;
            }
            task[this.editingField] = value;
          } else if (this.editingField === 'taskName') {
            const value = task[this.editingField];
            if (!value || value.trim() === '') {
              this.$toast.fail('任务名称不能为空');
              return;
            }
            task[this.editingField] = value.trim();
          }
          
          // 自动保存到本地存储
          this.saveTasks();
          
          console.log(`[WorkReport] 已保存任务 ${this.editingTaskId} 的字段 ${this.editingField} 修改`);
        }
      }
      
      this.editingTaskId = null;
      this.editingField = null;
    },
    
    // 快速编辑任务字段
    quickEditTask(taskId, field, value) {
      console.log(`[WorkReport] 快速编辑任务 ${taskId} 的字段 ${field} 为 ${value}`);
      
      const taskIndex = this.tasks.findIndex(task => task.id === taskId);
      if (taskIndex !== -1) {
        this.tasks[taskIndex][field] = value;
        this.saveTasks();
      }
    },
    
    // 测试表格编辑与API兼容性
    testTableEditing() {
      console.log('[WorkReport] 测试表格编辑功能与API兼容性');
      
      if (this.tasks.length === 0) {
        this.$toast.fail('请先添加一些任务以测试表格编辑功能');
        return;
      }
      
      // 测试数据结构
      const testTask = this.tasks[0];
      console.log('[WorkReport] 测试任务数据结构:', JSON.stringify(testTask, null, 2));
      
      // 检查必要字段
      const requiredFields = ['id', 'projectName', 'taskContent', 'regularHours', 'overtimeHours', 'progress'];
      const missingFields = requiredFields.filter(field => !Object.prototype.hasOwnProperty.call(testTask, field));
      
      if (missingFields.length > 0) {
        console.warn('[WorkReport] 缺少必要字段:', missingFields);
        this.$toast.fail(`任务数据缺少字段: ${missingFields.join(', ')}`);
      } else {
        console.log('[WorkReport] 任务数据结构完整，表格编辑功能兼容');
        this.$toast.success('表格编辑功能测试通过');
      }
      
      // 测试保存功能
      console.log('[WorkReport] 测试保存功能...');
      this.saveTasks();
    },
    onInlinePositionConfirm(value) {
      if (this.editingTaskId) {
        const taskIndex = this.tasks.findIndex(task => task.id === this.editingTaskId);
        if (taskIndex !== -1) {
          this.tasks[taskIndex].position = value.value;
          this.tasks[taskIndex].positionName = value.text;
          this.saveTasks();
          console.log(`[WorkReport] 已更新任务 ${this.editingTaskId} 的岗位信息: ${value.text}`);
        }
      }
      this.showInlinePositionPicker = false;
      this.stopEditing();
    },
    cancelInlinePositionEdit() {
      this.showInlinePositionPicker = false;
      this.stopEditing();
    },
    // 临时任务类型选择确认
    onInlineTmpTypeConfirm(value) {
      console.log('[DEBUG] onInlineTmpTypeConfirm 被调用');
      console.log('[DEBUG] 参数 value:', value);
      console.log('[DEBUG] 当前 editingTaskId:', this.editingTaskId);
      console.log('[DEBUG] 当前 editingField:', this.editingField);
      
      if (this.editingTaskId && this.editingField === 'tmpTaskTypeId') {
        const taskIndex = this.tasks.findIndex(task => task.id === this.editingTaskId);
        console.log('[DEBUG] 找到的任务索引:', taskIndex);
        
        if (taskIndex !== -1) {
          const task = this.tasks[taskIndex];
          console.log('[DEBUG] 赋值前的任务对象:', JSON.stringify(task, null, 2));
          
          // 使用响应式赋值
          this.$set(this.tasks[taskIndex], 'tmpTaskTypeId', value.value);
          this.$set(this.tasks[taskIndex], 'tmpTaskTypeName', value.text);
          
          console.log('[DEBUG] 赋值后的任务对象:', JSON.stringify(this.tasks[taskIndex], null, 2));
          
          // 临时注释掉saveTasks，测试赋值效果
          // this.saveTasks();
          
          console.log(`[WorkReport] 已更新任务 ${this.editingTaskId} 的临时任务类型: ${value.text}`);
          
          // 强制刷新视图
          this.$forceUpdate();
        } else {
          console.error('[DEBUG] 未找到对应的任务，taskId:', this.editingTaskId);
        }
      } else {
        console.error('[DEBUG] editingTaskId 或 editingField 不正确:', {
          editingTaskId: this.editingTaskId,
          editingField: this.editingField
        });
      }
      this.showInlineTmpTypePicker = false;
      this.stopEditing();
    },
    cancelInlineTmpTypeEdit() {
      this.showInlineTmpTypePicker = false;
      this.stopEditing();
    },
    // 耗费类型选择确认
    onInlineBurnOffTypeConfirm(value) {
      console.log('[DEBUG] onInlineBurnOffTypeConfirm 被调用');
      console.log('[DEBUG] 参数 value:', value);
      console.log('[DEBUG] 当前 editingTaskId:', this.editingTaskId);
      console.log('[DEBUG] 当前 editingField:', this.editingField);
      
      if (this.editingTaskId && this.editingField === 'burnOffTypeId') {
        const taskIndex = this.tasks.findIndex(task => task.id === this.editingTaskId);
        console.log('[DEBUG] 找到的任务索引:', taskIndex);
        
        if (taskIndex !== -1) {
          const task = this.tasks[taskIndex];
          console.log('[DEBUG] 赋值前的任务对象:', JSON.stringify(task, null, 2));
          
          // 使用响应式赋值
          this.$set(this.tasks[taskIndex], 'burnOffTypeId', value.value);
          this.$set(this.tasks[taskIndex], 'burnOffTypeName', value.text);
          
          console.log('[DEBUG] 赋值后的任务对象:', JSON.stringify(this.tasks[taskIndex], null, 2));
          
          // 临时注释掉saveTasks，测试赋值效果
          // this.saveTasks();
          
          console.log(`[WorkReport] 已更新任务 ${this.editingTaskId} 的耗费类型: ${value.text}`);
          
          // 强制刷新视图
          this.$forceUpdate();
        } else {
          console.error('[DEBUG] 未找到对应的任务，taskId:', this.editingTaskId);
        }
      } else {
        console.error('[DEBUG] editingTaskId 或 editingField 不正确:', {
          editingTaskId: this.editingTaskId,
          editingField: this.editingField
        });
      }
      this.showInlineBurnOffTypePicker = false;
      this.stopEditing();
    },
    cancelInlineBurnOffTypeEdit() {
      this.showInlineBurnOffTypePicker = false;
      this.stopEditing();
    },
    createNewTaskAndStartEditing() {
      console.log('[WorkReport] 创建新任务并开始内联编辑');
      
      // 生成新的任务ID
      const newTaskId = Date.now().toString();
      
      // 如果没有选择岗位，默认使用第一个岗位选项
      if (!this.taskForm.position && this.positionOptions.length > 0) {
        this.taskForm.position = this.positionOptions[0].value;
        this.taskForm.positionName = this.positionOptions[0].text;
      }
      
      // 创建新任务对象
      const newTask = {
        id: newTaskId,
        project: this.taskForm.project,
        projectName: this.taskForm.projectName,
        taskName: this.taskForm.taskName || '',
        taskCode: this.taskForm.taskCode || '',
        taskContent: this.taskForm.taskContent || '',
        regularHours: this.taskForm.regularHours || 0,
        overtimeHours: this.taskForm.overtimeHours || 0,
        progress: this.taskForm.progress || 0,
        position: this.taskForm.position || '',
        positionName: this.taskForm.positionName || '',
        date: this.formatDate(this.selectedDate),
        status: 'a',
        statusDisplay: '暂存',
        isProjectLevel: !this.taskForm.taskName && !this.taskForm.taskCode,
        plannedHours: this.taskForm.plannedHours || 0,
        startDate: this.taskForm.startDate || '',
        endDate: this.taskForm.endDate || '',
        plannedStartDate: this.taskForm.plannedStartDate || '',
        plannedEndDate: this.taskForm.plannedEndDate || '',
        // 添加用于显示的字段
        positionDisplay: this.taskForm.positionName || '未设置',
        progressDisplay: `${(this.taskForm.progress || 0).toFixed(0)}%`,
        // 添加任务类型字段
        taskType: this.taskForm.taskType || 'normal',
        // 添加FOWNPROJECT字段（用于敏捷任务）
        ownProject: this.taskForm.ownProject || ''
      };
      
      // 针对敏捷任务的特殊处理
      if (newTask.taskType === 'agile' && newTask.project) {
        newTask.ownProject = newTask.project;
        console.log(`[WorkReport] 敏捷任务设置FOWNPROJECT为项目编码: "${newTask.ownProject}"`);
      }
      
      console.log(`[WorkReport] 创建任务类型: ${newTask.taskType}, 任务名称: ${newTask.taskName}, 项目: ${newTask.projectName}`);
      
      // 添加到任务列表开头（最新的在上面）
      this.tasks.unshift(newTask);
      
      // 保存到本地存储
      this.saveTasks();
      
      // 退出添加模式
      this.isAddingNewTask = false;
      
      // 提示用户
      this.$toast.success('已创建新任务，请填写工作内容');
      
      // 确保DOM完全更新后再开始编辑
      this.$nextTick(() => {
        // 再等一个tick确保新任务的DOM元素已渲染
        this.$nextTick(() => {
          console.log('[WorkReport] 开始新任务的工作内容内联编辑');
          this.startEditing(newTaskId, 'taskContent');
        });
      });
    },
    toggleMultiSelectMode() {
      this.isMultiSelectMode = !this.isMultiSelectMode;
      
      // 清空已选择的项目
      this.selectedItems = [];
      
      this.$toast.success(`已${this.isMultiSelectMode ? '开启' : '关闭'}多选模式`);
    },
    isItemSelected(item) {
      // 多选模式：检查是否在selectedItems数组中
      return this.selectedItems.some(selectedItem => 
        selectedItem.itemType === item.itemType && selectedItem.value === item.value
      );
    },
    handleItemClick(item) {
      // 处理特殊选项（临时任务和耗费）
      if (item.isSpecial) {
        if (item.value === 'temp-task') {
          // 触发临时任务功能
          this.showCombinedPicker = false;
          this.createTempTaskInline();
          return;
        } else if (item.value === 'time-cost') {
          // 触发耗费功能
          this.showCombinedPicker = false;
          this.createTimeCostInline();
          return;
        }
      }
      
      // 多选模式下，避免重复调用toggleItemSelection
      // 复选按钮的点击事件会独立处理，这里直接返回
      if (this.isMultiSelectMode) {
        return;
      }
      
      // 非多选模式下的原有逻辑
      this.toggleItemSelection(item);
    },
    toggleItemSelection(item) {
      const index = this.selectedItems.findIndex(selectedItem => 
        selectedItem.itemType === item.itemType && selectedItem.value === item.value
      );
      
      if (index >= 0) {
        // 已选择，移除
        this.selectedItems.splice(index, 1);
      } else {
        // 未选择，添加
        this.selectedItems.push({
          ...item,
          // 确保包含所有必要的字段
          itemType: item.itemType,
          value: item.value,
          text: item.text,
          displayText: item.displayText,
          description: item.description,
          projectName: item.projectName,
          projectValue: item.projectValue,
          plannedHours: item.plannedHours,
          estimatedHours: item.estimatedHours,
          startDate: item.startDate || '',
          endDate: item.endDate || '',
          plannedStartDate: item.plannedStartDate || '',
          plannedEndDate: item.plannedEndDate || '',
          taskType: item.taskType
        });
      }
      
      console.log(`[WorkReport] 多选模式 - 当前已选择 ${this.selectedItems.length} 项`);
    },
    
    createMultipleTasksAndStartEditing() {
      console.log('[WorkReport] 批量创建任务并开始内联编辑');
      console.log(`[WorkReport] 准备创建 ${this.selectedItems.length} 个任务`);
      
      const createdTaskIds = [];
      
      // 如果没有选择岗位，默认使用第一个岗位选项
      const defaultPosition = this.positionOptions.length > 0 ? {
        value: this.positionOptions[0].value,
        text: this.positionOptions[0].text
      } : null;
      
      // 遍历选中的项目，为每个创建任务
      this.selectedItems.forEach((item, index) => {
        const newTaskId = (Date.now() + index).toString(); // 确保每个ID都不同
        
        // 为每个选中项目设置taskForm数据
        const itemTaskForm = {
          project: item.itemType === 'project' ? item.value : item.projectValue,
          projectName: item.itemType === 'project' ? item.text : item.projectName,
          taskName: item.itemType === 'task' ? item.text : '',
          taskCode: item.itemType === 'task' ? item.value : '',
          taskContent: item.description || '',
          regularHours: item.estimatedHours || 0,
          overtimeHours: 0,
          progress: 0,
          position: defaultPosition ? defaultPosition.value : '',
          positionName: defaultPosition ? defaultPosition.text : '',
          plannedHours: item.plannedHours || item.estimatedHours || 0,
          startDate: item.startDate || '',
          endDate: item.endDate || '',
          plannedStartDate: item.plannedStartDate || item.startDate || '',
          plannedEndDate: item.plannedEndDate || item.endDate || ''
        };
        
        // 创建新任务对象
        const newTask = {
          id: newTaskId,
          project: itemTaskForm.project,
          projectName: itemTaskForm.projectName,
          taskName: itemTaskForm.taskName,
          taskCode: itemTaskForm.taskCode,
          taskContent: itemTaskForm.taskContent,
          regularHours: itemTaskForm.regularHours,
          overtimeHours: itemTaskForm.overtimeHours,
          progress: itemTaskForm.progress,
          position: itemTaskForm.position,
          positionName: itemTaskForm.positionName,
          date: this.formatDate(this.selectedDate),
          status: 'a',
          statusDisplay: '暂存',
          isProjectLevel: item.itemType === 'project',
          plannedHours: itemTaskForm.plannedHours,
          startDate: itemTaskForm.startDate || '',
          endDate: itemTaskForm.endDate || '',
          plannedStartDate: itemTaskForm.plannedStartDate || '',
          plannedEndDate: itemTaskForm.plannedEndDate || '',
          // 添加用于显示的字段
          positionDisplay: itemTaskForm.positionName || '未设置',
          progressDisplay: `${(itemTaskForm.progress || 0).toFixed(0)}%`,
          taskType: item.taskType || 'normal'
        };
        
        // 添加到任务列表开头
        this.tasks.unshift(newTask);
        createdTaskIds.push(newTaskId);
        
        console.log(`[WorkReport] 已创建任务 ${index + 1}/${this.selectedItems.length}: ${item.displayText}`);
      });
      
      // 保存到本地存储
      this.saveTasks();
      
      // 清空选择状态并退出添加模式
      this.selectedItems = [];
      this.isMultiSelectMode = false;
      this.isAddingNewTask = false;
      this.showCombinedPicker = false;
      
      // 提示用户任务已创建，需要手动保存
      this.$toast.success(`已成功创建 ${createdTaskIds.length} 个任务，请编辑完成后点击保存按钮`);
      
      // 自动开始编辑第一个任务的工作内容
      if (createdTaskIds.length > 0) {
        this.$nextTick(() => {
          this.$nextTick(() => {
            console.log('[WorkReport] 开始第一个新任务的工作内容内联编辑');
            this.startEditing(createdTaskIds[0], 'taskContent');
          });
        });
      }
    },
    toggleProjectGroup(projectName) {
      console.log(`[WorkReport] 切换项目组 ${projectName} 的展开状态`);
      // 如果当前状态是undefined，则设为false（收起），否则取反
      this.$set(this.projectGroupStates, projectName, !this.projectGroupStates[projectName]);
    },
    onInlineProgressConfirm(value) {
      if (this.editingTaskId) {
        const taskIndex = this.tasks.findIndex(task => task.id === this.editingTaskId);
        if (taskIndex !== -1) {
          this.tasks[taskIndex].progress = value.value;
          this.saveTasks();
          console.log(`[WorkReport] 已更新任务 ${this.editingTaskId} 的进度: ${value.value}%`);
        }
      }
      this.showInlineProgressPicker = false;
      this.stopEditing();
    },
    cancelInlineProgressEdit() {
      this.showInlineProgressPicker = false;
      this.stopEditing();
    },
    canUndoTask(task) {
      // 检查任务状态，只允许撤销状态为'b'(已提交)的任务
      if (!task || !task.status) {
        console.warn('[WorkReport] canUndoTask: 任务或状态信息缺失', task);
        return false;
      }
      
      // 将状态转换为小写进行比较
      const status = (task.status || '').toLowerCase();
      const canUndo = status === 'b'; // 只允许撤销已提交状态的任务
      
      console.log(`[WorkReport] canUndoTask: 任务ID=${task.id}, 状态=${status}, 可撤销=${canUndo}`);
      
      return canUndo;
    },
    undoTask(task) {
      console.log(`[WorkReport] 准备撤销任务: ID=${task.id}, 项目="${task.projectName || task.project}", 任务="${task.taskName}", 状态="${task.status}"`);
      
      // 检查任务状态，只允许撤销状态为'b'(已提交)的任务
      if (!this.canUndoTask(task)) {
        const statusText = this.formatTaskStatus(task.status);
        console.log(`[WorkReport] 撤销被拒绝: 任务状态为"${statusText}"，不允许撤销`);
        this.$toast.fail(`无法撤销"${statusText}"状态的任务，只能撤销"已提交"状态的任务`);
        return;
      }
      
      this.$dialog.confirm({
        title: '确认撤销',
        message: `确定要撤销"${task.projectName || task.project} ${task.taskName ? '- ' + task.taskName : ''}"的提交吗？撤销后任务将回到暂存状态。`,
        confirmButtonText: '确认撤销',
        cancelButtonText: '取消',
        confirmButtonColor: '#ff976a'
      })
        .then(() => {
          // 用户确认撤销
          console.log('[WorkReport] 用户确认撤销');
          
          // 显示撤销加载状态
          this.$toast.loading({
            message: '正在撤销...',
            forbidClick: true,
          });
          
          // 获取用户信息
          const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
          if (!userInfo || (!userInfo.userEntityId && !userInfo.userId)) {
            this.$toast.clear();
            this.$toast.fail('用户信息不完整，请重新登录');
            return;
          }
          
          // 获取单据号
          const billNumber = task.fbillno || task.billNumber || task.id;
          console.log(`[WorkReport] 准备调用撤销API，单据号: ${billNumber}`);
          
          if (!billNumber) {
            this.$toast.clear();
            this.$toast.fail('无法撤销：未找到单据号');
            console.error('[WorkReport] 撤销失败：任务没有单据号');
            return;
          }
          
          // 准备撤销参数
          const params = {
            numbers: [billNumber],
            userId: userInfo.userEntityId || userInfo.userId // 优先使用FUserId，后备使用Fid
          };
          
          // 添加日志以便验证使用的是哪个ID
          console.log('[WorkReport] 撤销 - 用户ID信息 - userEntityId:', userInfo.userEntityId, 'userId:', userInfo.userId, '最终使用的userId:', params.userId);
          console.log('[WorkReport] 撤销 - 参数:', params);
          
          // 调用撤销API
          undoWorkReportData(params)
            .then(result => {
              this.$toast.clear();
              console.log('[WorkReport] 撤销API调用结果:', result);
              
              if (result.success) {
                // API撤销成功，更新本地状态
                const index = this.tasks.findIndex(t => t.id === task.id);
                if (index !== -1) {
                  this.tasks[index].status = 'a'; // 改为暂存状态
                  console.log(`[WorkReport] 已撤销任务，状态改为暂存: ${task.id}`);
                  
                  // 自动保存到本地存储
                  this.saveTasks();
                  
                  // 清除缓存
                  this.clearCache();
                  
                  // 显示成功提示
                  this.$toast.success('撤销成功，任务已回到暂存状态');
                  
                  // 强制刷新任务列表以确保数据同步
                  console.log('[WorkReport] 撤销成功后强制刷新任务列表');
                  this.cacheNeedsRefresh = true;
                  
                  setTimeout(() => {
                    this.loadTasks(true); // 强制刷新
                  }, 500);
                } else {
                  console.warn(`[WorkReport] 未在本地数组中找到要撤销的任务: ${task.id}`);
                  this.$toast.success('撤销成功（服务器端已撤销）');
                }
              } else {
                // API撤销失败
                console.error('[WorkReport] 撤销API失败:', result.message);
                this.$toast.fail(`撤销失败: ${result.message}`);
              }
            })
            .catch(error => {
              this.$toast.clear();
              console.error('[WorkReport] 撤销API调用异常:', error);
              this.$toast.fail(`撤销失败: ${error.message || '网络错误'}`);
            });
        })
        .catch(() => {
          console.log('[WorkReport] 用户取消撤销');
        });
    },
    showTempTaskPopup() {
      console.log('[WorkReport] 显示临时任务创建界面');
      
      // 设置为临时任务模式
      this.isAddingNewTask = true;
      this.isEditingTask = false;
      this.isTempTaskMode = true; // 标记为临时任务模式
      
      // 初始化临时任务表单
      this.taskForm = {
        taskName: '临时任务',
        taskCode: 'TEMP_' + Date.now(),
        taskContent: '',
        project: 'TEMP_PROJECT',
        projectName: '临时任务',
        task: '',
        regularHours: 0,
        overtimeHours: 0,
        progress: 0, // 临时任务默认完成
        date: this.formatDate(new Date()),
        isProjectLevel: false,
        isTempTask: true, // 标记为临时任务
        plannedHours: 0,
        startDate: '',
        endDate: '',
        plannedStartDate: '',
        plannedEndDate: '',
        position: '',
        positionName: '',
        allSearchResults: []
      };
      
      // 显示任务表单，让用户填写工作内容和工时
      this.showTaskForm = true;
      
      console.log('[WorkReport] 临时任务模式已启动，显示任务表单');
    },
    showTimeCostPopup() {
      console.log('[WorkReport] 显示耗费记录界面');
      
      // 设置为耗费工时模式
      this.isAddingNewTask = true;
      this.isEditingTask = false;
      this.isTimeCostMode = true; // 标记为耗费工时模式
      
      // 初始化耗费工时表单
      this.taskForm = {
        taskName: '耗费',
        taskCode: 'TIMECOST_' + Date.now(),
        taskContent: '',
        project: 'TIMECOST_PROJECT',
        projectName: '耗费',
        task: '',
        regularHours: 0,
        overtimeHours: 0,
        progress: 0, // 耗费工时默认完成
        date: this.formatDate(new Date()),
        isProjectLevel: false,
        isTimeCost: true, // 标记为耗费工时
        plannedHours: 0,
        startDate: '',
        endDate: '',
        plannedStartDate: '',
        plannedEndDate: '',
        position: '',
        positionName: '',
        allSearchResults: []
      };
      
      // 显示任务表单，让用户填写工作内容和工时
      this.showTaskForm = true;
      
      console.log('[WorkReport] 耗费模式已启动，显示任务表单');
    },
    showTodayTasksPopup() {
      console.log('[WorkReport] 获取选择日期的任务数据');

      // 获取当前选择的日期字符串
      const selectedDateStr = this.formatDate(this.selectedDate);

      console.log(`[WorkReport] 选择日期任务功能启动，选择的日期: ${selectedDateStr}`);

      // 获取当前用户信息
      const userInfo = JSON.parse(localStorage.getItem('work_report_user') || '{}');
      const userIdName = userInfo.userIdName; // 使用userIdName而不是username

      if (!userIdName) {
        console.error('[WorkReport] 无法获取用户userIdName信息');
        this.$toast.fail('无法获取用户信息，请重新登录');
        return;
      }

      console.log(`[WorkReport] 准备获取选择日期任务数据，用户userIdName: ${userIdName}`);

      // 显示加载提示
      const loadingToast = this.$toast.loading({
        message: '正在获取选择日期任务...',
        forbidClick: true,
        duration: 0
      });

      // 调用任务专用API获取数据
      getTodayTasksData(userIdName, selectedDateStr, { timeout: 30000 })
        .then(response => {
          loadingToast.clear();

          if (response && response.success && response.data && response.data.tasks) {
            const selectedDateTasks = response.data.tasks;
            console.log(`[WorkReport] 成功获取选择日期任务数据，任务数量: ${selectedDateTasks.length}`);

            // 智能数据合并：更新当前tasks数组
            if (selectedDateTasks.length > 0) {
              // 获取当前页面上其他日期的任务
              const otherDateTasks = this.tasks.filter(task => task.date !== selectedDateStr);
              // 获取当前页面上选择日期的现有任务
              const existingSelectedDateTasks = this.tasks.filter(task => task.date === selectedDateStr);

              console.log(`[WorkReport] 当前状态: 其他日期任务${otherDateTasks.length}个, 选择日期现有任务${existingSelectedDateTasks.length}个, 新获取任务${selectedDateTasks.length}个`);

              // 合并现有任务和新获取的任务
              const allSelectedDateTasks = [...existingSelectedDateTasks, ...selectedDateTasks];

              // 使用统一的去重方法处理所有选择日期的任务
              const deduplicatedSelectedDateTasks = this.deduplicateTasksByName(allSelectedDateTasks);

              // 计算新增任务数量
              const newTasksCount = deduplicatedSelectedDateTasks.length - existingSelectedDateTasks.length;
              const duplicateCount = selectedDateTasks.length - newTasksCount;

              console.log(`[WorkReport] 去重结果: 选择日期任务从${allSelectedDateTasks.length}个去重到${deduplicatedSelectedDateTasks.length}个`);
              console.log(`[WorkReport] 新增${newTasksCount}个任务, 跳过${duplicateCount}个重复任务`);

              // 更新任务列表
              this.tasks = [...otherDateTasks, ...deduplicatedSelectedDateTasks];
              console.log(`[WorkReport] 合并后总任务数: ${this.tasks.length} (其他日期: ${otherDateTasks.length}, 选择日期: ${deduplicatedSelectedDateTasks.length})`);

              // 保存到本地存储
              this.saveTasks();

              // 显示结果提示
              if (duplicateCount > 0) {
                this.$toast.success(`成功获取 ${newTasksCount} 个新任务，跳过 ${duplicateCount} 个重复任务`);
              } else if (newTasksCount > 0) {
                this.$toast.success(`成功获取 ${newTasksCount} 个选择日期任务`);
              } else {
                this.$toast('所有任务都已存在，未添加新任务');
              }
            } else {
              console.log('[WorkReport] 服务器返回选择日期暂无任务');
              this.$toast('选择日期暂无任务记录');
            }
          } else {
            console.warn('[WorkReport] 选择日期任务API返回数据格式异常:', response);

            // 降级到本地数据筛选
            const localSelectedDateTasks = this.tasks.filter(task => task.date === selectedDateStr);
            if (localSelectedDateTasks.length > 0) {
              this.$toast(`找到 ${localSelectedDateTasks.length} 个本地选择日期任务`);
            } else {
              this.$toast('选择日期暂无任务记录');
            }
          }
        })
        .catch(error => {
          loadingToast.clear();
          console.error('[WorkReport] 获取选择日期任务数据失败:', error);

          // 降级到本地数据筛选
          const localSelectedDateTasks = this.tasks.filter(task => task.date === selectedDateStr);
          if (localSelectedDateTasks.length > 0) {
            console.log(`[WorkReport] 选择日期任务API失败，使用本地数据，找到 ${localSelectedDateTasks.length} 个选择日期任务`);
            this.$toast(`网络异常，显示 ${localSelectedDateTasks.length} 个本地选择日期任务`);
          } else {
            console.log('[WorkReport] 选择日期任务API失败且无本地选择日期任务数据');
            this.$toast.fail('获取选择日期任务失败，请检查网络连接');
          }
        });

      // 滚动到任务列表顶部
      this.$nextTick(() => {
        const taskContainer = this.$el.querySelector('.task-list-container');
        if (taskContainer) {
          taskContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    },
    handleTouchStart(event) {
      this.touchStartX = event.touches[0].clientX;
      this.touchStartY = event.touches[0].clientY;
      this.touchStartTime = new Date().getTime();
      this.isSwiping = false;
    },
    handleTouchMove(event) {
      // 防止页面滚动
      event.preventDefault();
      this.isSwiping = true;
    },
    handleTouchEnd(event) {
      if (!this.isSwiping) return;
      
      const touchEndX = event.changedTouches[0].clientX;
      const touchEndY = event.changedTouches[0].clientY;
      const touchEndTime = new Date().getTime();
      
      const touchDuration = touchEndTime - this.touchStartTime;
      const touchDistanceX = touchEndX - this.touchStartX;
      const touchDistanceY = Math.abs(touchEndY - this.touchStartY);
      const touchDistanceAbsX = Math.abs(touchDistanceX);
      
      // 判断是否为有效的横向滑动：
      // 1. 滑动时间小于500ms
      // 2. 横向滑动距离大于阈值
      // 3. 横向滑动距离大于纵向滑动距离（确保是横向滑动）
      if (touchDuration < 500 && 
          touchDistanceAbsX > this.swipeThreshold && 
          touchDistanceAbsX > touchDistanceY) {
        
        if (touchDistanceX > 0) {
          // 向右滑动 - 切换到上个月
          console.log('[Calendar] 检测到向右滑动，切换到上个月');
          this.goToPreviousPeriod();
        } else {
          // 向左滑动 - 切换到下个月
          console.log('[Calendar] 检测到向左滑动，切换到下个月');
          this.goToNextPeriod();
        }
      }
      
      this.isSwiping = false;
    },
    toggleSearchTypeDropdown() {
      this.showSearchTypeDropdown = !this.showSearchTypeDropdown;
    },
    selectSearchType(option) {
      this.searchType = option.value;
      this.searchTypeText = option.text;
      this.showSearchTypeDropdown = false;
      
      // 更新过滤类型以保持一致性
      this.filterType = option.value;
      
      console.log(`[WorkReport] 搜索类型已更新为: ${option.text} (${option.value})`);
    },
    
    // 点击外部关闭下拉菜单
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.showSearchTypeDropdown = false;
      }
    },
    // 自动调整textarea高度
    autoResize(event) {
      const textarea = event.target;
      // 重置高度到最小值，然后根据内容调整
      textarea.style.height = '72px'; // 最小高度
      
      // 根据滚动高度调整，但不超过最大高度
      const newHeight = Math.min(textarea.scrollHeight, 200);
      if (newHeight > 72) {
        textarea.style.height = newHeight + 'px';
      }
    },
    updateTaskContent(taskId, content) {
      const task = this.tasks.find(t => t.id === taskId);
      if (task) {
        // 检查任务是否可编辑
        if (!this.canEditTask(task)) {
          console.log(`[WorkReport] 任务 ${taskId} 不可编辑，拒绝更新工作内容，状态: ${task.status}`);
          this.$toast.fail('已提交或已审核的任务不可编辑');
          return;
        }

        task.taskContent = content;
        this.saveTasks();
        console.log(`[WorkReport] 更新任务 ${taskId} 的工作内容: ${content}`);
      }
    },
    handleContentEditStart(taskId) {
      console.log(`[WorkReport] 任务 ${taskId} 开始编辑工作内容`);
      // 可以在这里添加编辑开始时的逻辑，比如记录编辑状态
    },
    handleContentEditEnd(taskId) {
      console.log(`[WorkReport] 任务 ${taskId} 结束编辑工作内容`);
      // 可以在这里添加编辑结束时的逻辑，比如自动保存
    },
    createTempTaskInline() {
      console.log('[WorkReport] 创建临时任务并开启内联编辑');
      
      // 生成新任务ID
      const newTaskId = Date.now().toString();
      
      // 创建临时任务对象
      const newTask = {
        id: newTaskId,
        project: 'TEMP_PROJECT',
        projectName: '临时任务',
        taskName: '临时任务',
        taskCode: 'TEMP_' + Date.now(),
        taskContent: '',
        regularHours: 0,
        overtimeHours: 0,
        progress: 100, // 临时任务默认完成
        position: '',
        positionName: '',
        date: this.formatDate(this.selectedDate),
        status: 'a', // 默认状态为暂存
        statusDisplay: '暂存',
        isProjectLevel: false,
        isTempTask: true, // 标记为临时任务
        plannedHours: 0,
        startDate: '',
        endDate: '',
        plannedStartDate: '',
        plannedEndDate: '',
        positionDisplay: '未设置',
        progressDisplay: '0%',
        taskType: 'temp',
        // 新增字段用于FOtherTaskName等API字段的映射
        otherTaskName: '', // 对应FOtherTaskName，为空时使用用户输入的taskName
        otherTaskType: '1', // 对应FOtherTaskType，1表示临时任务
        tmpTaskTypeId: '', // 对应FTmpTaskTypeId.FNUMBER，临时任务类型ID
        burnOffTypeId: '' // 对应FBurnOffTypeId.FNUMBER，耗费任务使用，临时任务为空
      };
      
      // 添加到任务列表开头
      this.tasks.unshift(newTask);
      
      // 保存到本地存储
      this.saveTasks();
      
      // 提示用户并自动开始编辑工作内容
      this.$toast.success('已创建临时任务，请填写工作内容');
      
      console.log('[WorkReport] 临时任务创建完成');
      
      // 自动开始编辑工作内容
      this.$nextTick(() => {
        this.$nextTick(() => {
          console.log('[WorkReport] 开始临时任务工作内容的内联编辑');
          this.startEditing(newTaskId, 'taskContent');
        });
      });
    },
    createTimeCostInline() {
      console.log('[WorkReport] 创建耗费记录并开启内联编辑');
      
      // 生成新任务ID
      const newTaskId = Date.now().toString();
      
      // 创建耗费任务对象
      const newTask = {
        id: newTaskId,
        project: 'TIMECOST_PROJECT',
        projectName: '工时消耗',
        taskName: '耗费',
        taskCode: 'TIMECOST_' + Date.now(),
        taskContent: '',
        regularHours: 0,
        overtimeHours: 0,
        progress: 100, // 耗费工时默认完成
        position: '',
        positionName: '',
        date: this.formatDate(this.selectedDate),
        status: 'a', // 默认状态为暂存
        statusDisplay: '暂存',
        isProjectLevel: false,
        isTimeCost: true, // 标记为耗费工时
        plannedHours: 0,
        startDate: '',
        endDate: '',
        plannedStartDate: '',
        plannedEndDate: '',
        positionDisplay: '未设置',
        progressDisplay: '0%',
        taskType: 'timecost',
        // 新增字段用于FOtherTaskName等API字段的映射
        otherTaskName: '', // 对应FOtherTaskName，为空时使用用户输入的taskName
        otherTaskType: '2', // 对应FOtherTaskType，2表示耗费任务
        tmpTaskTypeId: '', // 对应FTmpTaskTypeId.FNUMBER，临时任务使用，耗费任务为空
        burnOffTypeId: '' // 对应FBurnOffTypeId.FNUMBER，耗费任务类型ID
      };
      
      // 添加到任务列表开头
      this.tasks.unshift(newTask);
      
      // 保存到本地存储
      this.saveTasks();
      
      // 提示用户并自动开始编辑工作内容
      this.$toast.success('已创建耗费记录，请填写工作内容');
      
      console.log('[WorkReport] 耗费记录创建完成，该任务保存后将自动设置为已审核状态');
      
      // 自动开始编辑工作内容
      this.$nextTick(() => {
        this.$nextTick(() => {
          console.log('[WorkReport] 开始耗费记录工作内容的内联编辑');
          this.startEditing(newTaskId, 'taskContent');
        });
      });
    },
    startTmpTypeEditing(taskId) {
      this.editingTaskId = taskId;
      this.editingField = 'tmpTaskTypeId';
      this.showInlineTmpTypePicker = true;
    },
    startBurnOffTypeEditing(taskId) {
      this.editingTaskId = taskId;
      this.editingField = 'burnOffTypeId';
      this.showInlineBurnOffTypePicker = true;
    }
  }
};
</script>

<style lang="scss" scoped>
// 定义金蝶企业蓝色变量
$kingdee-blue: #276ff5;
$kingdee-blue-light: rgba(39, 111, 245, 0.1);
$kingdee-blue-bright: #276ff5;

.work-report-container {
  padding: 0px 0; // 只保留上下内边距，移除左右内边距
  background-color: #f5f5f5;
  min-height: 100vh;
  // 添加底部padding，为固定按钮留出空间（按钮高度40px + padding 15px*2 = 70px）
  padding-bottom: 90px;
  display: flex;
  flex-direction: column;
  
  .tab-nav {
    display: flex;
    background-color: #fff;
    height: 44px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    
    .tab-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 15px;
      color: #323233;
      position: relative;
      cursor: pointer;
      
      &.active {
        color: $kingdee-blue;
        font-weight: 500;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 2px;
          background-color: $kingdee-blue;
        }
      }
    }
    
    // 开发调试工具样式
    .dev-tools {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      
      .van-icon {
        font-size: 18px;
        color: #999;
        margin-left: 10px;
        padding: 5px;
        border-radius: 50%;
        
        &:active {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }
  
  .calendar-container {
    background-color: #f9f9f9;
    padding: 5px 15px 5px 15px; // 减少垂直内边距从10px到5px
    margin: 44px 0px 0 0px; // 添加顶部边距44px以避免被固定导航栏遮挡
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
    
    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0; // 移除左右内边距，因为容器已有内边距
      margin-bottom: 5px; // 减少下边距从10px到5px
      
      .calendar-title {
        font-size: 16px;
        font-weight: 500;
        color: $kingdee-blue;
      }
      
      .van-icon {
        font-size: 18px;
        color: $kingdee-blue;
        padding: 5px;
        border-radius: 50%;
        
        &:active {
          background-color: $kingdee-blue-light;
        }
      }
    }
    
    .calendar-nav {
      display: flex;
      justify-content: space-around;
      padding: 5px 0;
      border-bottom: 1px solid #ebedf0;
      
      .week-day {
        width: 14.28%;
        text-align: center;
        font-size: 14px;
        color: #646566;
        font-weight: 500;
      }
    }
    
    .calendar-days {
      display: flex;
      flex-wrap: wrap;
      padding: 5px 0; // 减少内边距从10px到5px
      
      // 月视图滑动优化
      &.month-calendar {
        user-select: none;
        touch-action: pan-x;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        
        // 改为网格布局，每行显示7个，自动换行
        display: grid;
        grid-template-columns: repeat(7, 1fr);

        padding: 5px;
      }
      
      .calendar-day {
        position: relative;
        cursor: pointer;
        
        .day-number {
          font-size: 16px;
          margin-bottom: 3px;
        }
        
        .task-approved-icon {
          font-size: 10px; // 稍微缩小图标尺寸
          color: #276ff5;
          position: absolute;
          bottom: 1px; // 移到底部，与红点保持一致
          left: 50%; // 移到中央
          transform: translateX(-50%); // 居中变换
        }
        
        .task-pending-indicator {
          width: 3px; // 稍微缩小尺寸
          height: 3px; // 稍微缩小尺寸
          background-color: #ff4757;
          border-radius: 50%;
          position: absolute;
          bottom: 1px; // 让它离字体更远，避免遮挡
          left: 50%; // 移回到中央
          transform: translateX(-50%); // 恢复居中变换
        }
        
        &.current-day .day-number {
          background-color: $kingdee-blue;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        &.selected-day {
          background-color: $kingdee-blue-light;
        }
        
        &.week-mode-day {
          width: 14.28%;
          height: 45px; // 调整为与月视图一致的正方形尺寸
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          
          .day-number {
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
          }
        }
        
        &.month-mode-day {
          height: 45px; // 调整为正方形尺寸
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          
          .day-number {
            width: 30px;
            height: 30px;
            text-align: center;
            line-height: 30px;
          }
          
          &.empty-day {
            cursor: default;
            
            &:hover {
              background-color: transparent;
            }
          }
          
          &.other-month-day {
            color: #c8c9cc;
            
            &:hover {
              background-color: rgba(200, 201, 204, 0.1);
            }
            
            .day-number {
              color: #c8c9cc;
            }
            
            // 其他月份的日期不显示任务状态指示器
            .task-approved-icon,
            .task-pending-indicator {
              display: none;
            }
          }
        }
      }
    }
    
    .year-calendar {
      padding: 8px 0; // 减少内边距从15px到8px
      
      .year-months {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        
        .year-month {
          width: 25%;
          height: 30px; // 减少高度从60px到30px
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 10px;
          font-size: 15px;
          cursor: pointer;
          border-radius: 4px;
          
          &:hover {
            background-color: $kingdee-blue-light;
          }
          
          &.current-month {
            color: white;
            background-color: $kingdee-blue;
          }
        }
      }
    }
    
    .calendar-mode-selector {
      display: flex;
      justify-content: center;
      margin-top: 8px; // 减少上边距从15px到8px
      padding-bottom: 3px; // 减少下内边距从5px到3px
      
      .mode-toggle-arrow {
        font-size: 18px;
        color: $kingdee-blue;
        cursor: pointer;
        
        &:hover {
          color: darken($kingdee-blue, 10%);
        }
      }
    }
  }
  
  .task-list-container {
    flex: 1;
    
    .task-date-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      background-color: #f2f3f5;
      border-radius: 4px;
      font-size: 14px;
      margin-bottom: 15px;
      
      .task-stats {
        font-size: 12px;
        color: #999;
        margin-right: auto;
        display: flex;
        align-items: center;
        
        .action-icon {
          font-size: 16px;
          margin-right: 10px;
          color: #ffffff;
          padding: 5px;
          border-radius: 50%;
          
          &:active {
            background-color: $kingdee-blue-light;
          }
        }
      }
      
      .action-buttons {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
        
        .action-btn {
          height: 30px;
          padding: 0 8px;
          border-radius: 4px;
          font-size: 12px;
          flex-shrink: 0;
          
          &:active {
            transform: scale(0.95);
          }
        }
        
        .add-task-btn,
        .today-task-btn,
        .temp-task-btn,
        .time-cost-btn {
          background-color: transparent;
          color: $kingdee-blue;
          border: none;
          
          &:active {
            background-color: rgba(39, 111, 245, 0.1);
          }
        }
        
        // 小屏幕适配
        @media screen and (max-width: 480px) {
          .action-btn {
            height: 28px;
            padding: 0 6px;
            font-size: 11px;
          }
        }
        
        // 极小屏幕适配 - 按钮可能需要换行
        @media screen and (max-width: 360px) {
          justify-content: flex-end;
          
          .action-btn {
            height: 26px;
            padding: 0 5px;
            font-size: 10px;
            min-width: 50px;
          }
        }
      }
    }
    
    // 卡片模式
    .task-list {
      // 项目分组样式
      .project-group {
        margin-bottom: 15px;
        
        .project-group-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 10px 6px 5px;
          background-color: #f7f8fa;
          border-radius: 8px;
          cursor: pointer;
          transition: background-color 0.2s ease;
          margin-bottom: 8px;
          
          &:hover {
            background-color: #ebedf0;
          }
          
          .project-info {
            flex: 1;
            
            .project-name {
              font-size: 16px;
              font-weight: 600;
              color: #323233;
              margin-left: 10px;
              margin-bottom: 4px;
            }
          }
          
          .project-toggle {
            margin-right: 200px; // 让折叠按钮往左移动3px
            
            .van-icon {
              font-size: 16px;
              color: #969799;
              transition: transform 0.2s ease;
            }
          }
        }
        
        .project-group-content {
          .task-item {
            margin-left: 0px;
            border-left: 3px solid #e1f3ff;
            
            &:hover {
              border-left-color: $kingdee-blue;
            }
          }
        }
      }
      
      .task-item {
        display: flex;
        justify-content: space-between;
        padding: 15px;
        background-color: white;
        border-radius: 8px;
        margin-bottom: 10px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative; // 添加相对定位，为绝对定位的按钮提供定位上下文
        
        &:hover {
          background-color: #f9f9f9;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          transform: translateY(-1px);
        }
        
        &:active {
          background-color: #f5f5f5;
          transform: translateY(0);
        }
        
        &.active {
          background-color: #f5f5f5;
          
          .task-info {
            .editable-field {
              .editable-value {
                background-color: inherit;
              }
            }
            
            .task-tmp-type,
            .task-burnoff-type {
              .editable-value {
                background-color: inherit;
              }
            }
          }
        }
        
        .task-content {
          flex: 1;
          display: grid;
          grid-template-rows: auto auto auto; // 让工作内容区域自动适应高度，移除100px限制
          grid-gap: 6px; // 减小网格间距从8px到6px
          // 移除padding-right，让内容区域完全占用空间
          
          .task-project {
            font-weight: 500;
            margin-bottom: 3px; // 减小底部边距从5px到3px
            font-size: 12px;
            color: #909399;
          }
          
          .task-name {
            font-weight: 500;
            font-size: 16px;
            color: #323233;
            display: flex;
            align-items: center;
            gap: 8px; // 添加元素间距
            
            .task-name-content {
              display: flex;
              align-items: center;
              flex: 1;
              min-width: 0; // 防止flex子项溢出
            }
            
            .project-level-tag {
              display: inline-block;
              padding: 2px 6px;
              background-color: #e8f3ff;
              color: $kingdee-blue;
              border-radius: 4px;
              font-size: 12px;
              margin-right: 5px;
            }
            
            .agile-task-tag {
              display: inline-block;
              padding: 2px 6px;
              background-color: #f0f9eb;
              color: #67c23a;
              border-radius: 4px;
              font-size: 12px;
              margin-right: 5px;
            }
            
            .task-status-tag {
              display: inline-block;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 12px;
              flex-shrink: 0; // 防止状态标签被压缩
              
              &.status-draft {
                background-color: #f2f3f5;
                color: #909399;
              }
              
              &.status-submitted {
                background-color: #e8f4ff;
                color: #1989fa;
              }
              
              &.status-approved {
                background-color: #f0f9eb;
                color: #67c23a;
              }
              
              &.status-reaudit {
                background-color: #fff2e8;
                color: #ff976a;
              }
            }
          }
          
          .task-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-gap: 4px 8px; // 调整间距以适应三列布局
            font-size: 12px;
            padding: 4px 0; // 减少内边距从6px 0到4px 0
            margin-bottom: 2px; // 减少底部外边距从4px到2px
            min-height: 40px; // 减小最小高度从44px到40px
            
            .editable-field {
              display: flex;
              flex-direction: column;
              min-height: 18px; // 适当增加最小高度从16px到18px，适应更大字体
              overflow: hidden; // 防止内容溢出
              
              .field-label {
                font-size: 13px; // 增大字体从11px到13px，提高可读性
                color: #909399;
                margin-bottom: 1px; // 减小间距
                flex-shrink: 0; // 防止标签被压缩
              }
              
              .editable-value, .inline-editing {
                color: #323233;
                font-size: 13px; // 增大字体从11px到13px，提高可读性
                line-height: 1.3;
                overflow: hidden; // 防止溢出
                text-overflow: ellipsis; // 超出显示省略号
                white-space: nowrap; // 不换行
                background-color: inherit;
              }
              
              .edit-hint {
                opacity: 0.6;
                font-size: 9px; // 减小字体
                margin-left: 4px;
                
                &::after {
                  content: "";
                }
              }
            }
            
            // 只读字段样式
            .readonly-field {
              display: flex;
              flex-direction: column;
              min-height: 18px;
              overflow: hidden;
              
              .field-label {
                font-size: 13px;
                color: #909399;
                margin-bottom: 1px;
                flex-shrink: 0;
              }
              
              .readonly-value {
                color: #646566; // 使用稍浅的颜色表示只读
                font-size: 13px;
                line-height: 1.3;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
            
            .task-hours {
              color: #000000;
            }
            
            .task-overtime {
              color: #000000;
            }
            
            .task-progress, .task-position {
              color: #000000;
            } 
            
            .task-ownproject {
              grid-column: 1 / -1; // 跨越所有列
              color: #909399;
              background-color: #f5f5f5;
              padding: 1px 4px;
              border-radius: 2px;
              display: inline-block;
              max-width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            
            /* FOtherTaskType 显示样式 */
            .task-other-type {
              grid-column: 1 / -1; /* 跨越所有列 */
              color: $kingdee-blue;
              background-color: inherit;
              padding: 1px 4px;
              border-radius: 2px;
              display: inline-block;
              max-width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-weight: 500;
            }
            
            /* 临时任务类型显示样式 */
            .task-tmp-type {
              color: $kingdee-blue;
              background-color: transparent !important;
              border-radius: 2px;
              display: inline-block;
              max-width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              
              .editable-value {
                background-color: transparent !important;
              }
            }
            
            /* 耗费类型显示样式 */
            .task-burnoff-type {
              color: $kingdee-blue;
              background-color: transparent !important;
              border-radius: 2px;
              display: inline-block;
              max-width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              
              .editable-value {
                background-color: transparent !important;
              }
            }
          }
          
          .task-description {
            // 简化样式，由WorkContentEditor组件内部控制具体样式
            margin-top: auto; // 确保在Grid布局中贴底
            min-height: 20px; // 减小最小高度，与组件保持一致
            // 移除max-height限制，让内容完全展示
            // 组件内部会处理所有的编辑状态和样式
          }
        }
        
        .task-actions {
          display: flex;
          flex-direction: row; // 水平排列按钮
          align-items: center;
          gap: 5px; // 按钮之间的间距
          
          .van-icon {
            font-size: 18px;
            color: #969799;
            cursor: pointer;
            
            &:hover {
              color: #323233;
            }
            
            &:active {
              color: #1989fa;
            }
          }
          
          .van-icon[name="delete"] {
            &:hover {
              color: #ee0a24;
            }
          }
          
          .van-icon[name="revoke"] {
            &:hover {
              color: #ff976a;
            }
          }
        }
      }
    }
    
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30px 0;
    }
  }
  
  .task-form {
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #ebedf0;
      
      .form-title {
        font-size: 16px;
        font-weight: 500;
      }
      
      .van-icon {
        font-size: 18px;
      }
    }
    
    .form-content {
      flex: 1;
      padding: 15px;
      overflow-y: auto;
      
      // 优化所有表单输入框尺寸
      ::v-deep .van-field {
        .van-field__control {
          font-size: 13px; // 减小字体
          padding: 6px 8px; // 减小内边距
          min-height: 32px; // 设置较小的最小高度
        }
        
        // 特别优化textarea
        textarea {
          font-size: 13px; // 减小字体
          min-height: 60px; // 减小textarea最小高度
          line-height: 1.4; // 设置行高
          padding: 6px 8px; // 减小内边距
        }
      }
      
      .form-item {
        margin-bottom: 15px; // 减小间距
        
        .form-label {
          font-size: 13px; // 减小字体
          color: #323233;
          margin-bottom: 6px; // 减小间距
        }
        
        .field-with-action {
          display: flex;
          align-items: center;
          
          .van-field {
            flex: 1;
            
            .field-label {
              display: inline-block;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 11px; // 减小字体
              margin-right: 4px;
              
              &:empty {
                display: none;
              }
            }
          }
          
          .view-details-btn {
            margin-left: 10px;
            height: 32px; // 减小高度
            font-size: 11px; // 减小字体
            background-color: $kingdee-blue-light;
            color: $kingdee-blue;
            border: none;
          }
        }
        
        .hours-input {
          display: flex;
          align-items: center;
          
          // 优化步进器尺寸
          ::v-deep .van-stepper {
            .van-stepper__input {
              font-size: 13px; // 减小字体
              height: 28px; // 减小高度
            }
            
            .van-stepper__minus,
            .van-stepper__plus {
              width: 28px; // 减小宽度
              height: 28px; // 减小高度
              font-size: 16px; // 减小字体
            }
          }
        }
        
        .progress-input {
          display: flex;
          align-items: center;
          
          .van-slider {
            flex: 1;
            margin-right: 15px;
          }
          
          .progress-input-right {
            display: flex;
            align-items: center;
            min-width: 80px;
            
            .progress-field {
              width: 60px;
              
              ::v-deep .van-field__control {
                text-align: right;
                font-size: 14px;
              }
            }
            
            .progress-symbol {
              font-size: 14px;
              color: $kingdee-blue;
              margin-left: 2px;
            }
          }
        }
        
        .date-range-input {
          display: flex;
          flex-direction: column;
          
          .date-field {
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
      
      .project-level-option {
        margin-top: 8px;
        padding: 8px;
        background-color: #f5f5f5;
        border-radius: 4px;
        
        .van-checkbox {
          font-size: 14px;
          color: #323233;
        }
      }
      
      .task-select-tip {
        margin-top: 8px;
        font-size: 12px;
        color: #ff976a;
        display: flex;
        align-items: center;
        
        .van-icon {
          margin-right: 4px;
        }
      }
    }
    
    .form-footer {
      padding: 15px;
      border-top: 1px solid #ebedf0;
    }
    
    .custom-picker {
      background-color: #fff;
      
      .custom-picker-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 44px;
        padding: 0 16px;
        background-color: #f7f8fa;
        
        .cancel {
          color: #969799;
          font-size: 14px;
        }
        
        .title {
          flex: 1;
          text-align: center;
          
          .main-title {
          font-size: 16px;
            font-weight: 500;
            color: #323233;
          }
        }
        
        .confirm {
          color: $kingdee-blue;
          font-size: 14px;
        }
      }
      
      .custom-picker-content {
        max-height: 300px;
        overflow-y: auto;
        
        .custom-picker-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #f5f5f5;
          
          &.active {
            background-color: #f5f5f5;
            
            .item-title {
              color: #333333;
              font-weight: 500;
            }
          }
          
          .item-content {
            flex: 1;
            
            .item-title {
              font-size: 14px;
              margin-bottom: 4px;
            }
            
            .item-desc {
              font-size: 12px;
              color: #969799;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }
          
          .item-actions {
            margin-left: 10px;
            
            .van-icon {
              font-size: 20px;
              color: $kingdee-blue;
              padding: 5px;
            }
          }
        }
      }
    }
  }
  
  .task-details {
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .details-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #ebedf0;
      
      .details-title {
        font-size: 16px;
        font-weight: 500;
        color: $kingdee-blue;
      }
      
      .details-actions {
        display: flex;
        
        .van-icon {
          font-size: 18px;
          margin-left: 15px;
          padding: 5px;
          cursor: pointer;
          
          &:hover {
            color: $kingdee-blue;
          }
        }
      }
    }
    
    .details-content {
      flex: 1;
      padding: 15px;
      overflow-y: auto;
      
      .details-item {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f5f5f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .details-label {
          font-size: 14px;
          color: #969799;
          margin-bottom: 5px;
        }
        
        .details-value {
          font-size: 16px;
          color: #323233;
          line-height: 1.5;
          word-break: break-all;
          
          .project-level-tag {
            display: inline-block;
            padding: 2px 6px;
            background-color: #e8f3ff;
            color: $kingdee-blue;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 5px;
          }
          
          .agile-task-tag {
            display: inline-block;
            padding: 2px 6px;
            background-color: #f0f9eb;
            color: #67c23a;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 5px;
          }
          
          .progress-bar-container {
            height: 8px;
            background-color: #f2f3f5;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 5px;
            
            .progress-bar {
              height: 100%;
              background-color: #999999;
              border-radius: 3px;
              transition: width 0.3s ease;
            }
          }
          
          .progress-text {
            font-size: 14px;
            color: $kingdee-blue;
            font-weight: 500;
          }
          
          // FOWNPROJECT信息样式
          .fownproject-info {
            display: flex;
            flex-direction: column;
            
            div {
              margin-bottom: 5px;
              
              &:last-child {
                margin-bottom: 0;
              }
            }
            
            .info-label {
              font-size: 14px;
              color: #646566;
              margin-right: 5px;
            }
            
            .info-value {
              font-size: 14px;
              color: #323233;
              font-weight: 500;
              
              &.match-type {
                color: $kingdee-blue;
                background-color: $kingdee-blue-light;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 12px;
              }
            }
            
            .mapping-warning {
              display: flex;
              align-items: center;
              color: #ff976a;
              background-color: rgba(255, 151, 106, 0.1);
              padding: 5px 10px;
              border-radius: 4px;
              font-size: 12px;
              
              .van-icon {
                margin-right: 5px;
              }
            }
          }
        }
        
        .task-list-in-details {
          display: flex;
          flex-wrap: wrap;
          
          .task-in-details {
            background-color: #f2f3f5;
            color: $kingdee-blue;
            padding: 5px 10px;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
            cursor: pointer;
            
            &:hover {
              background-color: $kingdee-blue-light;
            }
          }
        }
      }
      
      .details-actions-footer {
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #ebedf0;
      }
    }
  }
  
  .bottom-buttons {
    display: flex;
    padding: 15px;
    background-color: white;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
    // 添加固定定位
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    
    .save-btn, .submit-btn {
      flex: 1;
      height: 40px;
    }
    
    .save-btn {
      margin-right: 10px;
      color: $kingdee-blue;
      border-color: $kingdee-blue;
    }
    
    .submit-btn {
      background-color: $kingdee-blue;
      border-color: $kingdee-blue;
    }
  }

  .combined-picker {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #fff;

  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 44px;
    padding: 0 16px;
      background-color: #f7f8fa;

  .cancel {
        color: #969799;
    font-size: 14px;
  }

  .title {
    flex: 1;
    text-align: center;

  .main-title {
    font-size: 16px;
    font-weight: 500;
          color: #323233;
        }
  }

  .header-actions {
    display: flex;
    align-items: center;

  .van-button {
    margin-right: 10px;
  }

  .confirm {
    color: $kingdee-blue;
    font-size: 14px;
    cursor: pointer;
        }
      }
  }

  .picker-search {
    padding: 8px 16px;
      background-color: #f7f8fa;

  .search-with-dropdown {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

  .custom-dropdown {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
          background-color: #f7f8fa; // 与搜索区域背景一致
          border: 1px solid #ebedf0;
    border-radius: 4px;
    margin-right: 8px;
    min-width: 80px;
    transition: all 0.2s ease;

          &:hover {
            background-color: #f2f3f5; // 悬停时稍微变深
  }

          &:active {
            background-color: #ebedf0;
  }

  .dropdown-text {
    font-size: 14px;
            color: #323233;
    margin-right: 5px;
    flex: 1;
  }

  .dropdown-arrow {
    font-size: 12px;
            color: #909399;
    transition: transform 0.2s ease;

            &.dropdown-open {
    transform: rotate(180deg);
            }
  }

          // 自定义下拉选项
  .dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
            background-color: #fff;
            border: 1px solid #ebedf0;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
    margin-top: 2px;

  .dropdown-option {
    padding: 8px 16px;
    font-size: 14px;
              color: #323233;
    cursor: pointer;
    transition: background-color 0.2s ease;
              
              &:hover {
                background-color: #f7f8fa;
              }
              
              &.active {
                background-color: #f5f5f5;
                color: #333333;
              }
              
              &:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

              &:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
              }
            }
          }
  }

  .van-search {
    flex: 1;
        }
      }
  }

  .picker-content {
    flex: 1;
    overflow-y: auto;

  .picker-item-wrapper {
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
    border-bottom: none;
  }

        // 当wrapper包含选中的项目时的特殊处理
        &.has-active-item {
    border-bottom: none !important;

          // 添加伪元素进一步确保没有边框
          &::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
            background-color: white; // 使用白色覆盖任何可能的边框
    z-index: 5;
          }
        }
  }

  .picker-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
        position: relative; // 添加相对定位，为伪元素提供定位上下文
        
        &.active {
          background-color: #f5f5f5;
          border-left: none !important; // 强制移除左边框，包括特殊项目的蓝色边框
          
          // 使用更大的伪元素完全覆盖父容器的底部边框
          &::after {
    content: '';
    position: absolute;
            bottom: -2px; // 向下扩展更多，确保完全覆盖
            left: -1px;   // 向左扩展，确保完全覆盖
            right: -1px;  // 向右扩展，确保完全覆盖
            height: 3px;  // 增加高度，确保完全覆盖
            background-color: #f5f5f5; // 与背景色相同，覆盖边框
            z-index: 10;  // 确保在最上层
          }
          
          .item-title {
            color: #333333;
    font-weight: 500;
  }

          .item-type {
            .type-tag {
              background-color: #f5f5f5;
            }
          }
        }
        
        &.project-item {
          background-color: #f9f9f9;
          
          &.active {
            background-color: #f5f5f5;
            
            .item-type {
              .type-tag {
                background-color: #f5f5f5;
              }
            }
          }
  }

  .item-content {
    flex: 1;

  .item-title {
    font-size: 15px;
    margin-bottom: 4px;
  }

  .item-desc {
    font-size: 12px;
            color: #969799;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: 4px;
  }

  .item-project {
    font-size: 12px;
            color: #666666;
          }
  }

  .item-type {
    display: flex;
    align-items: center;

  .type-tag {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 8px;
    background-color: inherit;
    color: $kingdee-blue;

            &.project, &.task, &.special {
    background-color: inherit;
  }

            &.special {
    font-weight: 500;
            }
  }

  .expand-icon {
    font-size: 20px;
            color: #666666;
    padding: 5px;
          }
  }

        &.multi-select-mode {
    padding-left: 8px;
  }

  .item-checkbox {
    margin-right: 12px;
    display: flex;
    align-items: center;
        }
  }

  .item-details-expanded {
        background-color: #f8f9fa;
        border-top: 1px solid #ebedf0;
    padding: 16px;
    animation: slideDown 0.3s ease;

  .detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

          &:last-child {
    margin-bottom: 0;
  }

  .detail-label {
    font-size: 14px;
            color: #646566;
    font-weight: 500;
    min-width: 80px;
  }

  .detail-value {
    font-size: 14px;
            color: #323233;
    text-align: right;
    flex: 1;
    margin-left: 10px;

  .progress-display {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;

  .progress-bar-mini {
    width: 60px;
    height: 6px;
                background-color: #f2f3f5;
    border-radius: 3px;
    overflow: hidden;

  .progress-fill {
    height: 100%;
                  background-color: #999999;
    border-radius: 3px;
    transition: width 0.3s ease;
                }
  }

  .progress-text {
    font-size: 12px;
    color: $kingdee-blue;
    font-weight: 500;
    min-width: 30px;
              }
            }
          }
        }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      padding: 0 16px;
    }
    to {
      opacity: 1;
      max-height: 300px;
      padding: 16px;
    }
  }

  .no-results {
    padding: 30px 0;
      }
    }
  }
  }

// 响应式设计
  @media screen and (min-width: 768px) {
    .work-report-container {
      .calendar-container,
      .task-list-container {
        max-width: 768px;
        margin: 0 auto;
      }
      
      .bottom-buttons {
        max-width: 768px;
        margin: 0 auto;
      }
    }
  }

  .hours-input-group {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
  }

  .hours-input {
    width: 48%;
  }

  .hours-label {
    margin-bottom: 5px;
    font-size: 14px;
  color: #666;
  }

  .progress-container {
    display: flex;
    align-items: center;
    margin-top: 5px;
  }

  .progress-value {
    width: 50px;
    text-align: right;
    margin-left: 10px;
    font-size: 14px;
  color: #666;
  }

  .form-section {
    margin-bottom: 15px;
}

// 移动端表格优化
@media screen and (max-width: 767px) {
  .work-report-container {
    .task-table-container {
      .task-table {
        .table-header {
          font-size: 10px;
          padding: 4px 2px;
          
          .table-header-cell {
            font-size: 10px;
            padding: 0 1px;
          }
        }
        
        .table-row {
          padding: 6px 2px;
          font-size: 10px;
          
          .table-cell {
            padding: 0 1px;
            
            .cell-content {
              font-size: 10px;
              
              &.editable {
                padding: 1px 2px;
                min-height: 8px;
              }
              
              .agile-tag {
                font-size: 8px;
                padding: 1px 2px;
                margin-left: 1px;
              }
              
              .overtime-hint {
                font-size: 8px;
                margin-left: 1px;
              }
              
              .status-tag {
                font-size: 8px;
                padding: 1px 3px;
              }
            }
            
            .cell-editing {
              ::v-deep .van-field {
                .van-field__control {
                  font-size: 10px;
                  padding: 1px 2px;
                  min-height: 18px;
                  line-height: 1.2;
                }
              }
            }
          }
        }
      }
    }
    
    // 在非常小的屏幕上进一步压缩
    @media screen and (max-width: 400px) {
      .task-table-container {
        .task-table {
          .table-header {
            padding: 2px 1px;
            
            .table-header-cell {
              font-size: 9px;
              padding: 0 1px;
            }
          }
          
          .table-row {
            padding: 4px 1px;
            
            .table-cell {
              padding: 0 1px;
              
              .cell-content {
                font-size: 9px;
                line-height: 1.1;
                
                &.editable {
                  padding: 1px 1px;
                  min-height: 6px;
                }
              }
              
              .cell-editing {
                ::v-deep .van-field {
                  .van-field__control {
                    font-size: 9px;
                    padding: 1px;
                    min-height: 16px;
                    line-height: 1.1;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 卡片模式内联编辑样式
.task-list {
  .task-item {
    .task-description {
      // 添加工作内容底部样式
      margin-top: auto; // 确保在Grid布局中贴底
      padding: 4px 6px; // 进一步减小内边距从6px到4px 6px
      background-color: #f9f9f9;
      border-radius: 4px; // 减小圆角从6px到4px
      min-height: 20px; // 减小最小高度，与组件保持一致
      // 移除max-height限制，让内容完全展示
      // max-height: 100px; // 删除这个限制
      // overflow: hidden; // 移除溢出隐藏，让内容完全展示
    }
    
    .task-info {
      // 网格布局下的编辑字段样式
      .editable-field {
        // 重写为网格布局兼容的样式
        display: flex;
        flex-direction: column;
        min-height: 18px;
        overflow: hidden;
        
        .field-label {
          font-size: 13px;
          color: #909399;
          margin-bottom: 1px;
          flex-shrink: 0;
        }
        
        .editable-value {
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.2s;
          border: 1px solid transparent;
          min-height: 18px;
          display: flex;
          align-items: center;
          background-color: inherit;

          &:hover {
            background-color: #f7f8fa;
            border-color: #e1f3ff;

            .edit-hint {
              opacity: 1;
            }
          }

          &:active {
            background-color: #ebedf0;
          }

          &.disabled-field {
            cursor: not-allowed;
            color: #c8c9cc;
            opacity: 0.6;

            &:hover {
              background-color: inherit;
              border-color: transparent;
            }

            &:active {
              background-color: inherit;
            }
          }
        }
      }
      
      /* 临时任务类型显示样式 */
      .task-tmp-type {
        .editable-value {
          background-color: inherit;
        }
      }
      
      /* 耗费类型显示样式 */
      .task-burnoff-type {
        .editable-value {
          background-color: inherit;
        }
      }
    }
  }
}

// 进度选择弹窗
.van-popup.van-popup--bottom {
  padding-bottom: 20px;
}

.van-picker {
  padding: 10px;
}

.van-picker__toolbar {
  padding: 10px 0;
}

.van-picker__item {
  padding: 5px 0;
}

.van-picker__item--selected {
  color: $kingdee-blue;
}

.van-picker__toolbar .van-picker__confirm {
  color: $kingdee-blue;
}

.van-picker__toolbar .van-picker__cancel {
  color: #909399;
}

// 强制覆盖搜索类型下拉菜单的样式
.search-type-dropdown {
  .van-dropdown-menu__title {
    background-color: #f7f8fa !important;
    
    &:hover {
      background-color: #f2f3f5 !important;
    }
    
    &.van-dropdown-menu__title--active {
      background-color: #f7f8fa !important;
    }
  }
  
  .van-dropdown-item__option {
    background-color: #f7f8fa !important;
    
    &.van-dropdown-item__option--active {
      background-color: #f7f8fa !important;
    }
  }
}

// 自定义textarea样式
.custom-textarea {
  width: 100%;
  background-color: #fff;
  border: 1px solid $kingdee-blue;
  border-radius: 4px;
  padding: 4px 6px; // 调整内边距与工作内容区域一致
  font-size: 12px;
  line-height: 1.3; // 与工作内容区域保持一致
  min-height: 60px; // 适当减小最小高度
  max-height: 120px; // 保持合理的最大高度
  resize: none; // 禁用手动调整大小
  outline: none;
  font-family: inherit;
  overflow-y: auto; // 允许滚动
  
  &::placeholder {
    color: #c8c9cc;
    font-size: 12px;
  }
  
  &:focus {
    border-color: $kingdee-blue;
    box-shadow: 0 0 0 2px rgba(25, 137, 250, 0.1);
  }
}

// 表单中的textarea样式
.form-textarea {
  background-color: #f7f8fa;
  border: 1px solid #ebedf0;
  padding: 4px 6px; // 与custom-textarea保持一致
  font-size: 12px; // 与工作内容区域保持一致
  line-height: 1.3; // 与工作内容区域保持一致
  min-height: 48px; // 减小最小高度
  max-height: 100px; // 与工作内容区域保持一致
  overflow-y: auto; // 允许滚动编辑
  
  &:focus {
    background-color: #fff;
    border-color: $kingdee-blue;
  }
}

.custom-select {
  display: inline-block;
  padding: 4px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.custom-select:hover {
  border-color: #999;
}

.custom-select:focus {
  border-color: #4d90fe;
  outline: none;
}

.custom-select option {
  padding: 10px;
  background-color: #fff;
  color: #333;
}

.custom-select option:hover {
  background-color: #f0f0f0;
}

.custom-select option:checked {
  background-color: #4d90fe;
  color: #fff;
}

// 自定义input样式
.custom-input {
  background-color: #fff;
  border: 1px solid $kingdee-blue;
  border-radius: 2px;
  padding: 0px 1px;
  font-size: 11px;
  line-height: 1.3;
  height: 16px;
  outline: none;
  font-family: inherit;
  text-align: left;
  
  &.hours-input {
    width: 25px;
    max-width: 25px;
  }
  
  &.task-name-input {
    width: 100px;
    max-width: 150px;
    min-width: 80px;
  }
  
  &::placeholder {
    color: #c8c9cc;
    font-size: 11px;
  }
  
  &:focus {
    border-color: $kingdee-blue;
    box-shadow: 0 0 0 1px rgba(25, 137, 250, 0.1);
  }
}

// 自定义选择框样式
.custom-select {
  display: inline-block;
  padding: 0px 1px;
  border: none;
  border-radius: 2px;
  background-color: transparent;
  font-size: 11px;
  line-height: 1.3;
  height: 16px;
  cursor: pointer;
  text-align: left;
  
  &.progress-select {
    width: 20px;
    max-width: 20px;
  }
  
  &.position-select {
    width: 50px;
    max-width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  &:hover {
    border-color: $kingdee-blue;
  }
  
  &:focus {
    border-color: $kingdee-blue;
    outline: none;
  }
}

.picker-item {
  border-bottom: 1px solid #ebedf0;
  background-color: #fff;
  padding: 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &.active {
    background-color: #f5f5f5;
  }
  
  &.expanded {
    border-bottom: none;
  }
  
  .item-checkbox {
    margin-right: 12px;
  }
  
  .item-content {
    flex: 1;
    min-width: 0;
    
    .item-title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .item-desc {
      font-size: 14px;
      color: #969799;
      line-height: 1.4;
      margin-bottom: 4px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .item-project {
      font-size: 12px;
      color: #666666;
      background-color: #f2f3f5;
      padding: 2px 6px;
      border-radius: 3px;
      display: inline-block;
    }
  }
  
  .item-type {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    
    .type-tag {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      background-color: inherit;
      color: $kingdee-blue;
    }
    
    .expand-icon {
      font-size: 16px;
      color: #666666;
      padding: 5px;
      transition: transform 0.3s ease, color 0.3s ease;
      
      &:hover {
        color: $kingdee-blue;
      }
    }
  }
}

// 新增：展开详情区域样式
.item-details-expanded {
  background-color: #f9fafb;
  border: 1px solid #ebedf0;
  border-top: none;
  padding: 15px;
  margin-bottom: 10px;
  animation: slideDown 0.3s ease;
  
  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .detail-label {
      font-size: 14px;
      color: #646566;
      font-weight: 500;
      min-width: 80px;
    }
    
    .detail-value {
      font-size: 14px;
      color: #323233;
      text-align: right;
      flex: 1;
      margin-left: 10px;
      
      .progress-display {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 8px;
        
        .progress-bar-mini {
          width: 60px;
          height: 6px;
          background-color: #f2f3f5;
          border-radius: 3px;
          overflow: hidden;
          
          .progress-fill {
            height: 100%;
            background-color: #999999;
            border-radius: 3px;
            transition: width 0.3s ease;
          }
        }
        
        .progress-text {
          font-size: 12px;
          color: $kingdee-blue;
          font-weight: 500;
          min-width: 30px;
        }
      }
    }
  }
}

// 展开动画
@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 300px;
    transform: translateY(0);
  }
}

// 修改原有的item-type样式，更新expand-icon
.item-type {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  
  .type-tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background-color: inherit;
    color: $kingdee-blue;
  }
  
  .expand-icon {
    font-size: 16px;
    color: #666666;
    padding: 5px;
    transition: transform 0.3s ease, color 0.3s ease;

    &:hover {
      color: $kingdee-blue;
    }
  }

  // 任务详情已报工时相关样式
  .loading-text {
    color: #1989fa;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .error-text {
    color: #ee0a24;
    font-size: 14px;
  }

  .details-hours-breakdown {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
  }
}
</style>