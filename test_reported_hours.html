<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已报工时字段测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .expected {
            color: #28a745;
        }
        .actual {
            color: #007bff;
        }
        .error {
            color: #dc3545;
        }
        .info {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>已报工时字段测试页面</h1>
        <p class="info">此页面用于测试修改后的 FPROJECTID 和 FTASKID 字段是否正确保存和使用。</p>
        
        <div class="test-section">
            <div class="test-title">测试数据结构</div>
            <div class="test-data">
根据您提供的数据结构：
{
    "fseq": 1,
    "FDIV": 0,
    "FPROJECTID": 101580,  // 项目ID，用于传给已报工时
    "FTASKID": 0,          // 任务ID，0表示项目级别工作
    "FCODE": "PRJ-01-0007",
    "FNAME": "LX工业机器人项目",
    "FPRECENTCOMPLETE": 30,
    "FCATEGORY": "项目",
    "FOWNPROJID": 101580,
    "FOWNPROJECT": "LX工业机器人项目",
    "FOWNTASK": "",
    "FOBJECTID": 101580,
    "FPLANNEDST": "2023-06-27T08:00:00",
    "FPLANNEDED": "2026-12-30T18:00:00",
    "FPlanWorkHours": 0,
    "FTOTTIME": 0
}
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">修改说明</div>
            <div class="test-data">
1. 在 formatProjectAndTaskData 函数中添加了 fprojectId 和 ftaskId 字段
2. 修改了 getTaskRealReportedHours 方法的ID选择逻辑
3. 修改了 getTaskDisplayReportedHours 方法的ID选择逻辑  
4. 修改了 getTaskDetailsReportedHours 方法的ID选择逻辑
5. 添加了详细的调试日志

ID选择逻辑：
- 如果 ftaskId > 0，使用 ftaskId（具体任务）
- 如果 ftaskId = 0，使用 fprojectId（项目级别工作）
- 如果都没有，回退到原有逻辑
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">测试步骤</div>
            <div class="test-data">
1. 打开浏览器开发者工具的控制台
2. 访问工时报告页面
3. 查看控制台日志，确认以下信息：
   - 项目数据格式化时是否正确保存了 fprojectId 和 ftaskId
   - 任务数据格式化时是否正确保存了 fprojectId 和 ftaskId
   - 已报工时查询时是否使用了正确的ID
   - 已报工时API返回的数据是否正确解析

预期日志示例：
[API] 项目ID字段: FPROJECTID="101580", FTASKID="0"
[API] 任务ID字段: FPROJECTID="101580", FTASKID="12345"
[DEBUG] 使用项目ID (fprojectId): 101580
[DEBUG] 使用任务ID (ftaskId): 12345
[API] ✓ 找到已报工时字段: Property0, 值: 26, 类型: number
[API] 从字段 Property0 获取已报工时: 26
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">已报工时数据格式验证</div>
            <div class="test-data">
根据您提供的已报工时API返回数据：
{
  "data": [
    {
      "Property0": 26.0000000000
    }
  ]
}

验证要点：
✓ 数据结构：response.data.data 数组格式
✓ 字段名称：Property0 包含已报工时数值
✓ 数值类型：26.0000000000 应转换为 26
✓ 格式化结果：返回 { reportedHours: 26, ... }
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">验证要点</div>
            <div class="test-data expected">
✓ 项目级别工作（FTASKID=0）应该使用 FPROJECTID 查询已报工时
✓ 具体任务（FTASKID>0）应该使用 FTASKID 查询已报工时
✓ 控制台应该显示正确的ID选择日志
✓ 已报工时数据应该能够正确显示
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">注意事项</div>
            <div class="test-data info">
- 确保后端API返回的数据包含 FPROJECTID 和 FTASKID 字段
- 如果字段不存在，系统会回退到原有的ID选择逻辑
- 所有修改都保持了向后兼容性
- 建议在测试环境中先验证功能正常后再部署到生产环境
            </div>
        </div>
    </div>

    <script>
        console.log('已报工时字段测试页面已加载');
        console.log('请访问工时报告页面并查看控制台日志以验证修改效果');
    </script>
</body>
</html>
